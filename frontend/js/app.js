// 应用主要 JavaScript 代码

// 全局变量
let currentTaskId = null;
let pollingInterval = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadConfig();
    loadTaskHistory();
    setupEventListeners();

    // 从 localStorage 恢复当前任务
    const savedTaskId = localStorage.getItem('currentTaskId');
    if (savedTaskId) {
        currentTaskId = savedTaskId;
        checkTaskStatus(savedTaskId);
    }

    // 检查是否有从示例页面传来的Schema
    const selectedSchema = localStorage.getItem('selectedSchema');
    if (selectedSchema) {
        document.getElementById('extraction-schema').value = selectedSchema;
        localStorage.removeItem('selectedSchema');
        showSection('extract');
    }

    // 检查URL hash
    if (window.location.hash) {
        const section = window.location.hash.substring(1);
        if (['config', 'extract', 'history'].includes(section)) {
            showSection(section);
        }
    }
});

// 设置事件监听器
function setupEventListeners() {
    // 配置表单提交
    document.getElementById('config-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveConfig();
    });
    
    // 文件选择变化
    document.getElementById('pdf-file').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file && file.size > 50 * 1024 * 1024) {
            showAlert('文件大小不能超过 50MB', 'danger');
            e.target.value = '';
        }
    });
}

// 显示指定的界面部分
function showSection(sectionName) {
    // 隐藏所有部分
    document.querySelectorAll('.section').forEach(section => {
        section.style.display = 'none';
    });
    
    // 显示指定部分
    document.getElementById(sectionName + '-section').style.display = 'block';
    
    // 更新导航状态
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // 特殊处理
    if (sectionName === 'history') {
        loadTaskHistory();
    }
}

// 保存配置到 localStorage
function saveConfig() {
    const config = {
        llm_base_url: document.getElementById('llm-base-url').value,
        llm_api_key: document.getElementById('llm-api-key').value,
        llm_model: document.getElementById('llm-model').value,
        llm_temperature: parseFloat(document.getElementById('llm-temperature').value),
        llm_max_tokens: parseInt(document.getElementById('llm-max-tokens').value),
        llm_timeout: 60,
        max_retries: 3,
        retry_delay: 1.0,
        max_chunk_size: parseInt(document.getElementById('max-chunk-size').value),
        chunk_overlap: 200,
        enable_parallel: document.getElementById('enable-parallel').checked,
        max_parallel_chunks: parseInt(document.getElementById('max-parallel-chunks').value)
    };
    
    localStorage.setItem('extractionConfig', JSON.stringify(config));
    showAlert('配置已保存', 'success');
}

// 从 localStorage 加载配置
function loadConfig() {
    const savedConfig = localStorage.getItem('extractionConfig');
    if (savedConfig) {
        const config = JSON.parse(savedConfig);
        
        document.getElementById('llm-base-url').value = config.llm_base_url || 'https://api.openai.com/v1';
        document.getElementById('llm-api-key').value = config.llm_api_key || '';
        document.getElementById('llm-model').value = config.llm_model || 'gpt-4';
        document.getElementById('llm-temperature').value = config.llm_temperature || 0.1;
        document.getElementById('llm-max-tokens').value = config.llm_max_tokens || 4000;
        document.getElementById('max-chunk-size').value = config.max_chunk_size || 8000;
        document.getElementById('max-parallel-chunks').value = config.max_parallel_chunks || 5;
        document.getElementById('enable-parallel').checked = config.enable_parallel !== false;
    }
}

// 开始提取
async function startExtraction() {
    const fileInput = document.getElementById('pdf-file');
    const schemaInput = document.getElementById('extraction-schema');
    
    // 验证输入
    if (!fileInput.files[0]) {
        showAlert('请选择 PDF 文件', 'warning');
        return;
    }
    
    if (!schemaInput.value.trim()) {
        showAlert('请输入提取 Schema', 'warning');
        return;
    }
    
    // 验证配置
    const config = JSON.parse(localStorage.getItem('extractionConfig') || '{}');
    if (!config.llm_api_key) {
        showAlert('请先配置 API 密钥', 'warning');
        showSection('config');
        return;
    }
    
    try {
        // 解析 Schema
        const schema = JSON.parse(schemaInput.value);
        
        // 准备表单数据
        const formData = new FormData();
        formData.append('file', fileInput.files[0]);
        
        const extractionRequest = {
            schema: schema,
            config: config,
            prompt_template: document.getElementById('prompt-template').value || null,
            custom_instructions: document.getElementById('custom-instructions').value || null
        };
        
        formData.append('extraction_request', JSON.stringify(extractionRequest));
        
        // 显示加载状态
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<span class="loading-spinner"></span> 上传中...';
        button.disabled = true;
        
        // 发送请求
        const response = await fetch('/api/upload/pdf', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (response.ok) {
            currentTaskId = result.task_id;
            localStorage.setItem('currentTaskId', currentTaskId);
            
            showAlert(`文件上传成功，任务 ID: ${result.task_id}`, 'success');
            
            // 开始轮询任务状态
            startPolling(result.task_id);
            
            // 显示任务状态模态框
            showTaskModal(result.task_id);
            
        } else {
            showAlert(`上传失败: ${result.detail || result.message}`, 'danger');
        }
        
    } catch (error) {
        console.error('提取失败:', error);
        showAlert(`提取失败: ${error.message}`, 'danger');
    } finally {
        // 恢复按钮状态
        const button = document.querySelector('[onclick="startExtraction()"]');
        button.innerHTML = '<i class="bi bi-play-fill"></i> 开始提取';
        button.disabled = false;
    }
}

// 开始轮询任务状态
function startPolling(taskId) {
    if (pollingInterval) {
        clearInterval(pollingInterval);
    }
    
    pollingInterval = setInterval(() => {
        checkTaskStatus(taskId);
    }, 2000); // 每2秒检查一次
}

// 停止轮询
function stopPolling() {
    if (pollingInterval) {
        clearInterval(pollingInterval);
        pollingInterval = null;
    }
}

// 检查任务状态
async function checkTaskStatus(taskId) {
    try {
        const response = await fetch(`/api/tasks/${taskId}/status`);
        const status = await response.json();
        
        if (response.ok) {
            updateTaskStatus(status);
            
            // 如果任务完成或失败，停止轮询
            if (status.status === 'completed' || status.status === 'failed') {
                stopPolling();
                if (status.status === 'completed') {
                    loadTaskResult(taskId);
                }
            }
        }
    } catch (error) {
        console.error('检查任务状态失败:', error);
    }
}

// 更新任务状态显示
function updateTaskStatus(status) {
    const content = document.getElementById('task-status-content');
    if (!content) return;
    
    const statusClass = getStatusClass(status.status);
    const progressBar = status.progress > 0 ? 
        `<div class="progress mb-3">
            <div class="progress-bar" style="width: ${status.progress}%">${status.progress.toFixed(1)}%</div>
         </div>` : '';
    
    content.innerHTML = `
        <div class="mb-3">
            <strong>任务 ID:</strong> ${status.task_id}
        </div>
        <div class="mb-3">
            <strong>状态:</strong> 
            <span class="badge ${statusClass}">${getStatusText(status.status)}</span>
        </div>
        ${progressBar}
        <div class="mb-3">
            <strong>文件名:</strong> ${status.filename || '未知'}
        </div>
        <div class="mb-3">
            <strong>创建时间:</strong> ${new Date(status.created_at).toLocaleString()}
        </div>
        ${status.message ? `<div class="mb-3"><strong>消息:</strong> ${status.message}</div>` : ''}
        ${status.error_message ? `<div class="alert alert-danger"><strong>错误:</strong> ${status.error_message}</div>` : ''}
    `;
}

// 显示任务模态框
function showTaskModal(taskId) {
    const modal = new bootstrap.Modal(document.getElementById('task-modal'));
    modal.show();
    
    // 立即检查一次状态
    checkTaskStatus(taskId);
}

// 加载任务结果
async function loadTaskResult(taskId) {
    try {
        const response = await fetch(`/api/tasks/${taskId}/result`);
        const result = await response.json();
        
        if (response.ok && result.extracted_data) {
            const content = document.getElementById('task-status-content');
            content.innerHTML += `
                <hr>
                <h6>提取结果:</h6>
                <div class="json-viewer">
                    <pre>${JSON.stringify(result.extracted_data, null, 2)}</pre>
                </div>
                <div class="mt-3">
                    <button class="btn btn-outline-primary btn-sm" onclick="downloadResult('${taskId}')">
                        <i class="bi bi-download"></i> 下载结果
                    </button>
                </div>
            `;
        }
    } catch (error) {
        console.error('加载任务结果失败:', error);
    }
}

// 下载结果
async function downloadResult(taskId) {
    try {
        const response = await fetch(`/api/tasks/${taskId}/result`);
        const result = await response.json();
        
        if (response.ok) {
            const dataStr = JSON.stringify(result, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `extraction_result_${taskId}.json`;
            link.click();
        }
    } catch (error) {
        console.error('下载结果失败:', error);
        showAlert('下载失败', 'danger');
    }
}

// 加载任务历史
async function loadTaskHistory() {
    try {
        const response = await fetch('/api/tasks/?limit=20');
        const tasks = await response.json();
        
        const container = document.getElementById('task-history-list');
        
        if (tasks.length === 0) {
            container.innerHTML = '<div class="text-center text-muted">暂无任务记录</div>';
            return;
        }
        
        container.innerHTML = tasks.map(task => `
            <div class="task-item">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-2">
                            <strong class="me-2">${task.filename || '未知文件'}</strong>
                            <span class="badge ${getStatusClass(task.status)}">${getStatusText(task.status)}</span>
                        </div>
                        <div class="file-info">
                            <div>任务 ID: ${task.task_id}</div>
                            <div>创建时间: ${new Date(task.created_at).toLocaleString()}</div>
                            ${task.file_size ? `<div>文件大小: ${formatFileSize(task.file_size)}</div>` : ''}
                        </div>
                    </div>
                    <div class="task-actions">
                        <button class="btn btn-outline-primary btn-sm" onclick="showTaskModal('${task.task_id}')">
                            <i class="bi bi-eye"></i> 查看
                        </button>
                        ${task.status === 'completed' ? 
                            `<button class="btn btn-outline-success btn-sm" onclick="downloadResult('${task.task_id}')">
                                <i class="bi bi-download"></i> 下载
                             </button>` : ''}
                    </div>
                </div>
            </div>
        `).join('');
        
    } catch (error) {
        console.error('加载任务历史失败:', error);
        document.getElementById('task-history-list').innerHTML = 
            '<div class="alert alert-danger">加载任务历史失败</div>';
    }
}

// 工具函数
function getStatusClass(status) {
    const classes = {
        'pending': 'bg-secondary',
        'processing': 'bg-primary',
        'completed': 'bg-success',
        'failed': 'bg-danger'
    };
    return classes[status] || 'bg-secondary';
}

function getStatusText(status) {
    const texts = {
        'pending': '等待中',
        'processing': '处理中',
        'completed': '已完成',
        'failed': '失败'
    };
    return texts[status] || '未知';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showAlert(message, type = 'info') {
    // 创建警告框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}
