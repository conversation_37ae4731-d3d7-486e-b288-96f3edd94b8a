/* 自定义样式 */

body {
    background-color: #f8f9fa;
}

.navbar-brand {
    font-weight: bold;
}

.section {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.progress {
    height: 8px;
}

.task-item {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: white;
    transition: box-shadow 0.15s ease-in-out;
}

.task-item:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.status-pending {
    background-color: #6c757d;
}

.status-processing {
    background-color: #0d6efd;
}

.status-completed {
    background-color: #198754;
}

.status-failed {
    background-color: #dc3545;
}

.file-info {
    font-size: 0.875rem;
    color: #6c757d;
}

.task-actions {
    display: flex;
    gap: 0.5rem;
}

.json-viewer {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    max-height: 400px;
    overflow-y: auto;
}

.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f4f6;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.alert-custom {
    border-radius: 0.5rem;
    border: none;
    padding: 1rem 1.5rem;
}

.btn-custom {
    border-radius: 0.375rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.navbar-nav .nav-link {
    border-radius: 0.375rem;
    margin: 0 0.25rem;
    transition: background-color 0.15s ease-in-out;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.accordion-button:not(.collapsed) {
    background-color: #e7f1ff;
    color: #0c63e4;
}

.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.text-muted {
    color: #6c757d !important;
}

.text-success {
    color: #198754 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-warning {
    color: #fd7e14 !important;
}

.text-info {
    color: #0dcaf0 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }
    
    .card {
        margin: 0.5rem 0;
    }
    
    .task-actions {
        flex-direction: column;
    }
    
    .btn {
        margin-bottom: 0.5rem;
    }
}

/* PDF预览相关样式 */
.pdf-preview-container {
    height: 600px;
    overflow: hidden;
    border-radius: 0.375rem;
}

.pdf-upload-placeholder {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
}

.pdf-viewer {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.pdf-controls {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    flex-shrink: 0;
}

.pdf-canvas-container {
    flex: 1;
    overflow: auto;
    background-color: #e9ecef;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 20px;
}

#pdf-canvas {
    border: 1px solid #dee2e6;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    background-color: white;
    max-width: 100%;
    height: auto;
}

/* 任务状态卡片样式 */
.task-status-card {
    border-left: 4px solid #0d6efd;
}

.task-status-card.status-processing {
    border-left-color: #0d6efd;
}

.task-status-card.status-completed {
    border-left-color: #198754;
}

.task-status-card.status-failed {
    border-left-color: #dc3545;
}

.task-status-card.status-pending {
    border-left-color: #6c757d;
}

/* Schema保存按钮组 */
.schema-controls {
    display: flex;
    gap: 0.5rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .pdf-preview-container {
        height: 400px;
    }
    
    .pdf-controls {
        padding: 0.5rem;
    }
    
    .pdf-controls .d-flex {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .schema-controls {
        flex-direction: column;
    }
}

/* 加载动画 */
.pdf-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: #f8f9fa;
}

.pdf-loading .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 任务状态进度条样式 */
.task-progress {
    height: 6px;
    border-radius: 3px;
    overflow: hidden;
    background-color: #e9ecef;
}

.task-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #0d6efd, #6610f2);
    transition: width 0.3s ease;
}

/* 结果展示样式 */
.extraction-result {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-top: 1rem;
}

.extraction-result pre {
    margin: 0;
    font-size: 0.875rem;
    max-height: 300px;
    overflow-y: auto;
}

/* 文件信息样式 */
.file-info-badge {
    background-color: #e7f1ff;
    color: #0c63e4;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    margin-right: 0.5rem;
}

