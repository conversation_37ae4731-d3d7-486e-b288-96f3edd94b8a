<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>使用示例 - Memect Insight Extractor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="bi bi-file-text"></i>
                Memect Insight Extractor
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.html">
                    <i class="bi bi-house"></i> 主页
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-10 mx-auto">
                <h1 class="mb-4">
                    <i class="bi bi-lightbulb"></i> 使用示例
                </h1>

                <!-- 合同信息提取示例 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="bi bi-file-earmark-text"></i> 合同信息提取</h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">
                            从合同PDF中提取关键信息，如公司名称、合同金额、签署日期、当事方信息等。
                        </p>
                        
                        <h6>Schema示例：</h6>
                        <div class="json-viewer">
                            <pre id="contract-schema">{
  "type": "object",
  "properties": {
    "company_name": {
      "type": "string",
      "description": "公司名称"
    },
    "contract_amount": {
      "type": "number",
      "description": "合同金额（万元）"
    },
    "contract_date": {
      "type": "string",
      "description": "合同签署日期，格式：YYYY-MM-DD"
    },
    "parties": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "name": {"type": "string", "description": "当事方名称"},
          "role": {"type": "string", "description": "角色（甲方/乙方）"}
        }
      },
      "description": "合同当事方信息"
    }
  },
  "required": ["company_name", "contract_amount"]
}</pre>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-outline-primary" onclick="copySchema('contract-schema')">
                                <i class="bi bi-clipboard"></i> 复制Schema
                            </button>
                            <button class="btn btn-primary" onclick="useSchema('contract-schema')">
                                <i class="bi bi-arrow-right"></i> 使用此Schema
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 财务报告提取示例 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="bi bi-graph-up"></i> 财务报告提取</h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">
                            从财务报告PDF中提取财务数据、比率指标、公司信息等结构化数据。
                        </p>
                        
                        <h6>Schema示例：</h6>
                        <div class="json-viewer">
                            <pre id="financial-schema">{
  "type": "object",
  "properties": {
    "company_info": {
      "type": "object",
      "properties": {
        "name": {"type": "string", "description": "公司名称"},
        "stock_code": {"type": "string", "description": "股票代码"}
      }
    },
    "financial_data": {
      "type": "object",
      "properties": {
        "revenue": {"type": "number", "description": "营业收入（万元）"},
        "net_profit": {"type": "number", "description": "净利润（万元）"},
        "total_assets": {"type": "number", "description": "总资产（万元）"}
      }
    },
    "financial_ratios": {
      "type": "object",
      "properties": {
        "roe": {"type": "number", "description": "净资产收益率（%）"},
        "debt_ratio": {"type": "number", "description": "资产负债率（%）"}
      }
    }
  },
  "required": ["company_info", "financial_data"]
}</pre>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-outline-primary" onclick="copySchema('financial-schema')">
                                <i class="bi bi-clipboard"></i> 复制Schema
                            </button>
                            <button class="btn btn-primary" onclick="useSchema('financial-schema')">
                                <i class="bi bi-arrow-right"></i> 使用此Schema
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 自定义提示词示例 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="bi bi-chat-dots"></i> 自定义提示词示例</h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">
                            通过自定义提示词可以提高提取的准确性和针对性。
                        </p>
                        
                        <h6>合同提取提示词：</h6>
                        <div class="json-viewer">
                            <pre id="contract-prompt">你是一个专业的合同分析专家。请仔细阅读合同内容，提取关键信息。

注意事项：
1. 金额请统一转换为万元单位
2. 日期格式统一为YYYY-MM-DD
3. 如果信息不明确，请设置为null
4. 确保提取信息的准确性

请严格按照提供的JSON Schema格式返回结果。</pre>
                        </div>
                        
                        <h6 class="mt-4">财务报告提示词：</h6>
                        <div class="json-viewer">
                            <pre id="financial-prompt">你是一个专业的财务分析师。请从财务报告中提取关键财务数据。

注意事项：
1. 金额数据保持原单位（万元），不要转换
2. 百分比数据只提取数值部分，不包含%符号
3. 比率数据保持小数格式
4. 如果某个数据未明确提及，请设置为null
5. 确保所有数值的准确性

请严格按照提供的JSON Schema格式返回结果。</pre>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-outline-primary" onclick="copyPrompt('contract-prompt')">
                                <i class="bi bi-clipboard"></i> 复制合同提示词
                            </button>
                            <button class="btn btn-outline-primary" onclick="copyPrompt('financial-prompt')">
                                <i class="bi bi-clipboard"></i> 复制财务提示词
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 使用步骤 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="bi bi-list-ol"></i> 使用步骤</h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li><strong>配置API</strong>：在配置管理页面设置LLM API相关参数</li>
                            <li><strong>准备文档</strong>：确保PDF文件清晰可读，大小不超过50MB</li>
                            <li><strong>定义Schema</strong>：根据需要提取的信息定义JSON Schema</li>
                            <li><strong>上传文档</strong>：选择PDF文件并粘贴Schema</li>
                            <li><strong>配置选项</strong>：可选择添加自定义提示词和处理指令</li>
                            <li><strong>开始提取</strong>：点击开始提取按钮，系统会自动处理</li>
                            <li><strong>查看结果</strong>：等待处理完成后查看和下载结果</li>
                        </ol>
                    </div>
                </div>

                <div class="text-center">
                    <a href="index.html" class="btn btn-primary btn-lg">
                        <i class="bi bi-arrow-left"></i> 返回主页开始使用
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function copySchema(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                showAlert('Schema已复制到剪贴板', 'success');
            }).catch(() => {
                showAlert('复制失败', 'danger');
            });
        }
        
        function copyPrompt(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                showAlert('提示词已复制到剪贴板', 'success');
            }).catch(() => {
                showAlert('复制失败', 'danger');
            });
        }
        
        function useSchema(elementId) {
            const element = document.getElementById(elementId);
            const schema = element.textContent;
            
            // 保存到localStorage
            localStorage.setItem('selectedSchema', schema);
            
            // 跳转到主页
            window.location.href = 'index.html#extract';
        }
        
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
