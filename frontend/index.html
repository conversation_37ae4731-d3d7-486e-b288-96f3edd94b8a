<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Memect Insight Extractor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-file-text"></i>
                Memect Insight Extractor
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#" onclick="showSection('config')">
                    <i class="bi bi-gear"></i> 配置管理
                </a>
                <a class="nav-link" href="#" onclick="showSection('extract')">
                    <i class="bi bi-upload"></i> 文档提取
                </a>
                <a class="nav-link" href="#" onclick="showSection('history')">
                    <i class="bi bi-clock-history"></i> 历史记录
                </a>
                <a class="nav-link" href="examples.html">
                    <i class="bi bi-lightbulb"></i> 使用示例
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 配置管理界面 -->
        <div id="config-section" class="section">
            <div class="row">
                <div class="col-md-8 mx-auto">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-gear"></i> API 配置管理</h5>
                        </div>
                        <div class="card-body">
                            <form id="config-form">
                                <div class="mb-3">
                                    <label for="llm-base-url" class="form-label">API 基础 URL</label>
                                    <input type="url" class="form-control" id="llm-base-url" 
                                           placeholder="https://api.openai.com/v1" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="llm-api-key" class="form-label">API 密钥</label>
                                    <input type="password" class="form-control" id="llm-api-key" 
                                           placeholder="输入您的 API 密钥" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="llm-model" class="form-label">模型名称</label>
                                    <select class="form-select" id="llm-model">
                                        <option value="gpt-4">GPT-4</option>
                                        <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                                        <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                                        <option value="qwen-plus">Qwen Plus</option>
                                    </select>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="llm-temperature" class="form-label">温度参数</label>
                                            <input type="number" class="form-control" id="llm-temperature" 
                                                   min="0" max="2" step="0.1" value="0.1">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="llm-max-tokens" class="form-label">最大 Token 数</label>
                                            <input type="number" class="form-control" id="llm-max-tokens" 
                                                   min="1000" max="32000" value="4000">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="max-chunk-size" class="form-label">最大文本块大小</label>
                                            <input type="number" class="form-control" id="max-chunk-size" 
                                                   min="1000" max="20000" value="8000">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="max-parallel-chunks" class="form-label">最大并行块数</label>
                                            <input type="number" class="form-control" id="max-parallel-chunks" 
                                                   min="1" max="10" value="5">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enable-parallel" checked>
                                        <label class="form-check-label" for="enable-parallel">
                                            启用并行处理
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="button" class="btn btn-secondary" onclick="loadConfig()">
                                        <i class="bi bi-arrow-clockwise"></i> 重置
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-lg"></i> 保存配置
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文档提取界面 -->
        <div id="extract-section" class="section" style="display: none;">
            <div class="row">
                <div class="col-md-10 mx-auto">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-upload"></i> PDF 文档信息提取</h5>
                        </div>
                        <div class="card-body">
                            <!-- 文件上传 -->
                            <div class="mb-4">
                                <label for="pdf-file" class="form-label">选择 PDF 文件</label>
                                <input type="file" class="form-control" id="pdf-file" accept=".pdf">
                                <div class="form-text">支持最大 50MB 的 PDF 文件</div>
                            </div>
                            
                            <!-- Schema 配置 -->
                            <div class="mb-4">
                                <label for="extraction-schema" class="form-label">提取 Schema (JSON 格式)</label>
                                <textarea class="form-control" id="extraction-schema" rows="8" 
                                          placeholder='请输入 JSON Schema，例如：
{
  "type": "object",
  "properties": {
    "company_name": {"type": "string", "description": "公司名称"},
    "amount": {"type": "number", "description": "金额"},
    "date": {"type": "string", "description": "日期"}
  },
  "required": ["company_name"]
}'></textarea>
                            </div>
                            
                            <!-- 高级选项 -->
                            <div class="accordion mb-4" id="advanced-options">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" 
                                                data-bs-toggle="collapse" data-bs-target="#advanced-collapse">
                                            高级选项
                                        </button>
                                    </h2>
                                    <div id="advanced-collapse" class="accordion-collapse collapse">
                                        <div class="accordion-body">
                                            <div class="mb-3">
                                                <label for="prompt-template" class="form-label">自定义提示词模板</label>
                                                <textarea class="form-control" id="prompt-template" rows="4" 
                                                          placeholder="可选：自定义提示词模板"></textarea>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="custom-instructions" class="form-label">自定义指令</label>
                                                <textarea class="form-control" id="custom-instructions" rows="3" 
                                                          placeholder="可选：额外的处理指令"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="button" class="btn btn-primary btn-lg" onclick="startExtraction()">
                                    <i class="bi bi-play-fill"></i> 开始提取
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 历史记录界面 -->
        <div id="history-section" class="section" style="display: none;">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="bi bi-clock-history"></i> 任务历史记录</h5>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="loadTaskHistory()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新
                    </button>
                </div>
                <div class="card-body">
                    <div id="task-history-list">
                        <!-- 任务列表将在这里动态加载 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务状态模态框 -->
    <div class="modal fade" id="task-modal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">任务状态</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="task-status-content">
                        <!-- 任务状态内容 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
