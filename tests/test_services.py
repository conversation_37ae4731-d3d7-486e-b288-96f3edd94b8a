"""服务层测试"""

import pytest
import tempfile
import os
from unittest.mock import Mock, patch

from src.services.pdf_processor import PDFProcessor
from src.services.task_service import TaskService
from src.models.schemas import ExtractionConfigRequest


class TestPDFProcessor:
    """PDF处理器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.processor = PDFProcessor(self.temp_dir)
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_save_uploaded_file(self):
        """测试保存上传文件"""
        content = b"test content"
        filename = "test.pdf"
        
        file_path = await self.processor.save_uploaded_file(content, filename)
        
        assert os.path.exists(file_path)
        with open(file_path, 'rb') as f:
            assert f.read() == content
    
    def test_get_file_info(self):
        """测试获取文件信息"""
        # 创建测试文件
        test_file = os.path.join(self.temp_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("test")
        
        info = self.processor.get_file_info(test_file)
        
        assert "size" in info
        assert "created_at" in info
        assert "modified_at" in info
        assert info["size"] == 4
    
    def test_cleanup_file(self):
        """测试清理文件"""
        # 创建测试文件
        test_file = os.path.join(self.temp_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("test")
        
        assert os.path.exists(test_file)
        
        self.processor.cleanup_file(test_file)
        
        assert not os.path.exists(test_file)


class TestTaskService:
    """任务服务测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.service = TaskService()
    
    def test_get_status_message(self):
        """测试状态消息获取"""
        assert self.service._get_status_message("pending", 0.0) == "等待处理"
        assert self.service._get_status_message("processing", 50.0) == "处理中 (50.0%)"
        assert self.service._get_status_message("completed", 100.0) == "处理完成"
        assert self.service._get_status_message("failed", 0.0) == "处理失败"
        assert self.service._get_status_message("unknown", 0.0) == "未知状态"


class TestExtractionConfig:
    """提取配置测试"""
    
    def test_valid_config(self):
        """测试有效配置"""
        config = ExtractionConfigRequest(
            llm_base_url="https://api.openai.com/v1",
            llm_api_key="test-key",
            llm_model="gpt-4"
        )
        
        assert config.llm_base_url == "https://api.openai.com/v1"
        assert config.llm_api_key == "test-key"
        assert config.llm_model == "gpt-4"
        assert config.llm_temperature == 0.1  # 默认值
        assert config.enable_parallel is True  # 默认值
    
    def test_invalid_temperature(self):
        """测试无效温度参数"""
        with pytest.raises(ValueError):
            ExtractionConfigRequest(
                llm_base_url="https://api.openai.com/v1",
                llm_api_key="test-key",
                llm_temperature=3.0  # 超出范围
            )
    
    def test_invalid_max_tokens(self):
        """测试无效最大token数"""
        with pytest.raises(ValueError):
            ExtractionConfigRequest(
                llm_base_url="https://api.openai.com/v1",
                llm_api_key="test-key",
                llm_max_tokens=0  # 必须大于0
            )


if __name__ == "__main__":
    pytest.main([__file__])
