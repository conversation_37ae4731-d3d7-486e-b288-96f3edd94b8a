"""测试Schema模型"""

import pytest

from memect_insight_extractor.models.schema_models import SchemaField, ExtractionSchema


class TestSchemaModels:
    """测试Schema模型"""

    def test_schema_field_basic(self):
        """测试Schema字段基本功能"""
        field = SchemaField(
            name="test_field",
            type="string",
            description="测试字段",
            required=True
        )
        
        assert field.name == "test_field"
        assert field.type == "string"
        assert field.description == "测试字段"
        assert field.required is True
        assert field.is_primary_key is False  # 默认值

    def test_schema_field_primary_key(self):
        """测试Schema字段主键功能"""
        field = SchemaField(
            name="id",
            type="string",
            description="主键ID",
            required=True,
            is_primary_key=True
        )
        
        assert field.is_primary_key is True
        assert field.required is True

    def test_extraction_schema_basic(self):
        """测试ExtractionSchema基本功能"""
        fields = [
            SchemaField(name="name", type="string", required=True),
            SchemaField(name="amount", type="number"),
        ]
        
        schema = ExtractionSchema(
            name="test_schema",
            fields=fields,
            json_schema={"type": "object"}
        )
        
        assert schema.name == "test_schema"
        assert len(schema.fields) == 2
        
        # 测试必需字段
        required_fields = schema.get_required_fields()
        assert required_fields == ["name"]

    def test_extraction_schema_primary_keys(self):
        """测试ExtractionSchema主键功能"""
        fields = [
            SchemaField(name="id", type="string", is_primary_key=True),
            SchemaField(name="name", type="string", required=True),
            SchemaField(name="amount", type="number"),
        ]
        
        schema = ExtractionSchema(
            name="test_schema",
            fields=fields,
            json_schema={"type": "object"}
        )
        
        # 测试主键字段
        primary_keys = schema.get_primary_key_fields()
        assert primary_keys == ["id"]

    def test_extraction_schema_multiple_primary_keys(self):
        """测试多个主键字段"""
        fields = [
            SchemaField(name="id1", type="string", is_primary_key=True),
            SchemaField(name="id2", type="string", is_primary_key=True),
            SchemaField(name="name", type="string", required=True),
        ]
        
        schema = ExtractionSchema(
            name="test_schema",
            fields=fields,
            json_schema={"type": "object"}
        )
        
        primary_keys = schema.get_primary_key_fields()
        assert set(primary_keys) == {"id1", "id2"}

    def test_get_field_by_name(self):
        """测试根据名称获取字段"""
        fields = [
            SchemaField(name="id", type="string", is_primary_key=True),
            SchemaField(name="name", type="string", required=True),
        ]
        
        schema = ExtractionSchema(
            name="test_schema",
            fields=fields,
            json_schema={"type": "object"}
        )
        
        # 测试获取存在的字段
        field = schema.get_field_by_name("id")
        assert field is not None
        assert field.name == "id"
        assert field.is_primary_key is True
        
        # 测试获取不存在的字段
        field = schema.get_field_by_name("nonexistent")
        assert field is None

    def test_to_json_schema(self):
        """测试转换为JSON Schema"""
        json_schema = {
            "type": "object",
            "properties": {
                "id": {"type": "string"},
                "name": {"type": "string"}
            },
            "required": ["id"]
        }
        
        fields = [
            SchemaField(name="id", type="string", is_primary_key=True, required=True),
            SchemaField(name="name", type="string"),
        ]
        
        schema = ExtractionSchema(
            name="test_schema",
            fields=fields,
            json_schema=json_schema
        )
        
        result = schema.to_json_schema()
        assert result == json_schema
