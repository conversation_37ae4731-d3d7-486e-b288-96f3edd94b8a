"""pytest配置文件"""

import asyncio
from unittest.mock import AsyncMock

import pytest


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_llm_client():
    """Mock LLM客户端"""
    client = AsyncMock()
    client.extract_text.return_value = '{"test": "result"}'
    return client


@pytest.fixture
def sample_schema():
    """通用测试Schema"""
    return {
        "type": "object",
        "properties": {
            "name": {"type": "string", "description": "名称"},
            "value": {"type": "number", "description": "数值"},
            "date": {"type": "string", "description": "日期"},
        },
        "required": ["name"],
    }
