"""测试离线tokenizer集成功能"""

import pytest

from memect_insight_extractor.config import ExtractionConfig
from memect_insight_extractor.core.text_splitter import TextSplitter
from memect_insight_extractor.utils.tokenizer_manager import get_tokenizer_manager


class TestOfflineTokenizerIntegration:
    """测试离线tokenizer集成功能"""

    def test_tokenizer_manager_availability(self):
        """测试tokenizer管理器可用性"""
        manager = get_tokenizer_manager()
        
        # 检查OpenAI tokenizer可用性
        openai_available = manager.is_available("openai")
        assert openai_available, "OpenAI tokenizer应该可用"
        
        # 检查Qwen tokenizer可用性
        qwen_available = manager.is_available("qwen")
        assert qwen_available, "Qwen tokenizer应该可用"

    def test_openai_tokenizer_functionality(self):
        """测试OpenAI tokenizer功能"""
        manager = get_tokenizer_manager()
        
        test_text = "Hello world! 你好世界！这是一个测试文本。"
        
        # 测试不同模型的token计数
        gpt4_tokens = manager.count_tokens(test_text, "openai", "gpt-4")
        gpt35_tokens = manager.count_tokens(test_text, "openai", "gpt-3.5-turbo")
        
        assert gpt4_tokens > 0, "GPT-4 token计数应该大于0"
        assert gpt35_tokens > 0, "GPT-3.5 token计数应该大于0"
        
        # token数应该在合理范围内
        assert 5 <= gpt4_tokens <= 50, f"GPT-4 token数应该在合理范围内: {gpt4_tokens}"
        assert 5 <= gpt35_tokens <= 50, f"GPT-3.5 token数应该在合理范围内: {gpt35_tokens}"

    def test_qwen_tokenizer_functionality(self):
        """测试Qwen tokenizer功能"""
        manager = get_tokenizer_manager()
        
        test_text = "Hello world! 你好世界！这是一个测试文本。"
        
        qwen_tokens = manager.count_tokens(test_text, "qwen")
        
        assert qwen_tokens > 0, "Qwen token计数应该大于0"
        assert 5 <= qwen_tokens <= 50, f"Qwen token数应该在合理范围内: {qwen_tokens}"

    def test_text_splitter_with_openai_tokenizer(self):
        """测试使用OpenAI tokenizer的文本分割器"""
        config = ExtractionConfig(
            llm_api_key="test-key",
            max_chunk_tokens=50,
            chunk_overlap_tokens=10,
            tokenizer_type="openai",
            llm_model="gpt-4"
        )
        
        splitter = TextSplitter(config)
        
        # 测试短文本
        short_text = "这是一个短文本。"
        assert not splitter.should_split(short_text), "短文本不应该被分割"
        
        # 测试长文本
        long_text = "这是一个很长的文本。" * 20
        assert splitter.should_split(long_text), "长文本应该被分割"
        
        chunks = splitter.split_text(long_text)
        assert len(chunks) > 1, "长文本应该被分割成多个块"
        
        # 验证每个块的token数
        for chunk in chunks[:-1]:  # 最后一个块可能较小
            assert chunk.token_count <= config.max_chunk_tokens, \
                f"块的token数不应超过限制: {chunk.token_count} > {config.max_chunk_tokens}"

    def test_text_splitter_with_qwen_tokenizer(self):
        """测试使用Qwen tokenizer的文本分割器"""
        config = ExtractionConfig(
            llm_api_key="test-key",
            max_chunk_tokens=50,
            chunk_overlap_tokens=10,
            tokenizer_type="qwen"
        )
        
        splitter = TextSplitter(config)
        
        # 测试长文本
        long_text = "这是一个很长的文本。" * 20
        chunks = splitter.split_text(long_text)
        
        assert len(chunks) > 1, "长文本应该被分割成多个块"
        
        # 验证每个块的token数
        for chunk in chunks[:-1]:  # 最后一个块可能较小
            assert chunk.token_count <= config.max_chunk_tokens, \
                f"块的token数不应超过限制: {chunk.token_count} > {config.max_chunk_tokens}"

    def test_markdown_splitting_with_tokenizer(self):
        """测试Markdown文本的token分割"""
        markdown_text = """# 第一章 介绍

这是第一章的内容，包含了一些基本的介绍信息。

## 1.1 背景

这里是背景信息的详细描述。

```python
def hello_world():
    print("Hello, World!")
    return "success"
```

## 1.2 目标

这里描述了项目的目标和期望。

# 第二章 实现

这是第二章的内容。

## 2.1 架构设计

系统架构的详细说明。

| 组件 | 功能 | 状态 |
|------|------|------|
| 前端 | 用户界面 | 完成 |
| 后端 | 业务逻辑 | 进行中 |
| 数据库 | 数据存储 | 完成 |

## 2.2 技术选型

技术栈的选择和理由。
"""

        config = ExtractionConfig(
            llm_api_key="test-key",
            max_chunk_tokens=100,
            tokenizer_type="openai"
        )

        splitter = TextSplitter(config)
        chunks = splitter.split_text(markdown_text)
        
        assert len(chunks) > 0, "应该产生至少一个文本块"
        
        # 验证每个块都有token计数
        for chunk in chunks:
            assert chunk.token_count is not None, "每个块都应该有token计数"
            assert chunk.token_count > 0, "token计数应该大于0"
        
        # 验证章节结构保护
        first_chunk = chunks[0].content
        assert "# 第一章 介绍" in first_chunk, "第一个块应该包含第一章标题"
        
        # 验证代码块完整性
        code_found = False
        for chunk in chunks:
            if "```python" in chunk.content:
                # 代码块应该完整
                assert "```" in chunk.content.split("```python")[1], "代码块应该完整"
                code_found = True
                break
        assert code_found, "应该找到代码块"

    def test_token_count_comparison(self):
        """测试不同tokenizer的token计数对比"""
        manager = get_tokenizer_manager()
        
        # 测试中英文混合文本
        mixed_text = "Hello world! 你好世界！This is a test. 这是一个测试。"
        
        openai_tokens = manager.count_tokens(mixed_text, "openai", "gpt-4")
        qwen_tokens = manager.count_tokens(mixed_text, "qwen")
        fallback_tokens = manager._estimate_tokens_from_chars(mixed_text)
        
        # 所有计数都应该大于0
        assert openai_tokens > 0, "OpenAI token计数应该大于0"
        assert qwen_tokens > 0, "Qwen token计数应该大于0"
        assert fallback_tokens > 0, "回退token计数应该大于0"
        
        # token数应该在合理范围内
        char_count = len(mixed_text)
        assert openai_tokens < char_count, "token数应该小于字符数"
        assert qwen_tokens < char_count, "token数应该小于字符数"

    def test_fallback_mechanism(self):
        """测试回退机制"""
        manager = get_tokenizer_manager()
        
        # 测试字符估算回退
        test_text = "Hello world! 你好世界！"
        fallback_tokens = manager._estimate_tokens_from_chars(test_text)
        
        assert fallback_tokens > 0, "回退机制应该返回合理的token数"
        
        # 中文字符应该按1:1计算
        chinese_chars = sum(1 for char in test_text if "\u4e00" <= char <= "\u9fff")
        other_chars = len(test_text) - chinese_chars
        expected_tokens = chinese_chars + max(1, other_chars // 4)
        
        assert fallback_tokens == expected_tokens, \
            f"回退计算应该正确: {fallback_tokens} != {expected_tokens}"

    def test_configuration_compatibility(self):
        """测试配置兼容性"""
        # 测试新配置参数
        new_config = ExtractionConfig(
            llm_api_key="test-key",
            max_chunk_tokens=2000,
            chunk_overlap_tokens=100,
            tokenizer_type="openai"
        )
        
        assert new_config.max_chunk_tokens == 2000
        assert new_config.chunk_overlap_tokens == 100
        assert new_config.tokenizer_type == "openai"

    def test_offline_capability(self):
        """测试离线能力"""
        from pathlib import Path
        
        # 检查tokenizer文件是否存在
        package_dir = Path(__file__).parent.parent / "src" / "memect_insight_extractor"
        tokenizers_dir = package_dir / "tokenizers"
        
        assert tokenizers_dir.exists(), "tokenizers目录应该存在"
        
        openai_dir = tokenizers_dir / "openai"
        qwen_dir = tokenizers_dir / "qwen"
        
        assert openai_dir.exists(), "OpenAI tokenizer目录应该存在"
        assert qwen_dir.exists(), "Qwen tokenizer目录应该存在"
        
        # 检查关键文件
        assert (openai_dir / "cl100k_base.tiktoken").exists(), "OpenAI tokenizer文件应该存在"
        assert (qwen_dir / "tokenizer.json").exists(), "Qwen tokenizer文件应该存在"
