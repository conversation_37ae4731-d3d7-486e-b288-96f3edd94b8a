"""测试主提取器"""

import asyncio
from unittest.mock import AsyncMock, patch

import pytest

from memect_insight_extractor import DocumentExtractor, ExtractionConfig
from memect_insight_extractor.models.extraction_result import ExtractionStatus


class TestDocumentExtractor:
    """测试文档提取器"""

    @pytest.fixture
    def config(self):
        """测试配置"""
        return ExtractionConfig(
            llm_api_key="test-key",
            llm_model="gpt-4",
            max_chunk_size=1000,
            enable_parallel=False,
        )

    @pytest.fixture
    def extractor(self, config):
        """测试提取器"""
        return DocumentExtractor(config)

    @pytest.fixture
    def sample_schema(self):
        """示例Schema"""
        return {
            "type": "object",
            "properties": {
                "company_name": {"type": "string", "description": "公司名称"},
                "amount": {"type": "number", "description": "金额"},
                "date": {"type": "string", "description": "日期"},
            },
            "required": ["company_name"],
        }

    @pytest.fixture
    def sample_text(self):
        """示例文本"""
        return """
        合同编号：2024001
        甲方：北京科技有限公司
        乙方：上海贸易有限公司
        合同金额：100000元
        签署日期：2024年1月15日
        """

    @pytest.mark.asyncio
    async def test_extract_success(self, extractor, sample_text, sample_schema):
        """测试成功提取"""
        # Mock LLM响应
        mock_response = {
            "company_name": "北京科技有限公司",
            "amount": 100000,
            "date": "2024年1月15日",
        }

        with patch.object(
            extractor.llm_client, "extract_text", new_callable=AsyncMock
        ) as mock_llm:
            mock_llm.return_value = '{"company_name": "北京科技有限公司", "amount": 100000, "date": "2024年1月15日"}'

            result = await extractor.extract(sample_text, sample_schema)

            assert result.status == ExtractionStatus.SUCCESS
            assert result.extracted_data is not None
            assert result.extracted_data["company_name"] == "北京科技有限公司"
            assert result.extracted_data["amount"] == 100000
            assert not result.has_errors()

    @pytest.mark.asyncio
    async def test_extract_with_validation_error(
        self, extractor, sample_text, sample_schema
    ):
        """测试校验错误"""
        # Mock 返回不符合Schema的响应
        with patch.object(
            extractor.llm_client, "extract_text", new_callable=AsyncMock
        ) as mock_llm:
            mock_llm.return_value = '{"invalid_field": "value"}'

            result = await extractor.extract(sample_text, sample_schema)

            assert result.status == ExtractionStatus.PARTIAL  # 有数据但有错误
            assert result.has_errors()

    @pytest.mark.asyncio
    async def test_extract_with_json_parse_error(
        self, extractor, sample_text, sample_schema
    ):
        """测试JSON解析错误"""
        with patch.object(
            extractor.llm_client, "extract_text", new_callable=AsyncMock
        ) as mock_llm:
            mock_llm.return_value = "invalid json response"

            result = await extractor.extract(sample_text, sample_schema)

            assert result.status == ExtractionStatus.FAILED
            assert result.has_errors()

    @pytest.mark.asyncio
    async def test_extract_long_text_splitting(self, extractor, sample_schema):
        """测试长文本分段"""
        # 创建超长文本 - 确保足够长以触发分段
        long_text = "这是一个很长的文档内容。" * 500  # 确保超过max_chunk_size很多

        with patch.object(
            extractor.llm_client, "extract_text", new_callable=AsyncMock
        ) as mock_llm:
            mock_llm.return_value = '{"company_name": "测试公司"}'

            result = await extractor.extract(long_text, sample_schema)

            # 应该触发分段处理
            assert "total_chunks" in result.metadata
            assert result.metadata["total_chunks"] > 1

    @pytest.mark.asyncio
    async def test_extract_with_llm_error(self, extractor, sample_text, sample_schema):
        """测试LLM调用错误"""
        with patch.object(
            extractor.llm_client, "extract_text", new_callable=AsyncMock
        ) as mock_llm:
            mock_llm.side_effect = Exception("API调用失败")

            result = await extractor.extract(sample_text, sample_schema)

            assert result.status == ExtractionStatus.FAILED
            assert result.has_errors()
            assert any("API调用失败" in error.error_message for error in result.errors)

    def test_config_validation(self):
        """测试配置验证"""
        # 测试有效配置
        config = ExtractionConfig(llm_api_key="test-key")
        assert config.llm_api_key == "test-key"
        assert config.llm_model == "gpt-4"  # 默认值

        # 测试无效温度
        with pytest.raises(ValueError):
            ExtractionConfig(llm_api_key="test-key", llm_temperature=3.0)

    def test_extraction_result_methods(self):
        """测试提取结果方法"""
        from memect_insight_extractor.models.extraction_result import (
            ExtractionResult,
            ExtractionStatus,
        )

        result = ExtractionResult(status=ExtractionStatus.SUCCESS)

        # 测试初始状态
        assert result.is_success()
        assert not result.has_errors()

        # 添加错误
        result.add_error("TEST_ERROR", "测试错误")
        assert result.has_errors()
        assert result.status == ExtractionStatus.FAILED  # 没有数据时状态应该变为FAILED
