"""API测试"""

import pytest
import json
import asyncio
from httpx import AsyncClient
from fastapi.testclient import TestClient

from src.main import app


class TestAPI:
    """API测试类"""
    
    def test_health_check(self):
        """测试健康检查接口"""
        with TestClient(app) as client:
            response = client.get("/health")
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
    
    def test_config_test(self):
        """测试配置测试接口"""
        with TestClient(app) as client:
            response = client.get("/api/config/test")
            assert response.status_code == 200
            data = response.json()
            assert "supported_models" in data
            assert "default_config" in data
    
    def test_upload_limits(self):
        """测试上传限制接口"""
        with TestClient(app) as client:
            response = client.get("/api/upload/limits")
            assert response.status_code == 200
            data = response.json()
            assert "max_file_size" in data
            assert "allowed_types" in data
    
    def test_upload_without_file(self):
        """测试无文件上传"""
        with TestClient(app) as client:
            response = client.post("/api/upload/pdf")
            assert response.status_code == 422  # 缺少必需参数
    
    def test_get_nonexistent_task(self):
        """测试获取不存在的任务"""
        with TestClient(app) as client:
            response = client.get("/api/tasks/nonexistent-task-id/status")
            assert response.status_code == 404
    
    def test_recent_tasks(self):
        """测试获取最近任务列表"""
        with TestClient(app) as client:
            response = client.get("/api/tasks/")
            assert response.status_code == 200
            data = response.json()
            assert isinstance(data, list)


if __name__ == "__main__":
    pytest.main([__file__])
