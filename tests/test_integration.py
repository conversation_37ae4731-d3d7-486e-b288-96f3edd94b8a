"""集成测试"""

import asyncio
from unittest.mock import AsyncMock, patch

import pytest

from memect_insight_extractor import DocumentExtractor, ExtractionConfig
from memect_insight_extractor.models.extraction_result import ExtractionStatus


class TestIntegration:
    """集成测试类"""

    @pytest.fixture
    def config(self):
        """测试配置"""
        return ExtractionConfig(
            llm_api_key="test-key",
            llm_model="gpt-4",
            max_chunk_size=1000,
            enable_parallel=False,
        )

    @pytest.fixture
    def sample_contract_text(self):
        """示例合同文本"""
        return """
        合同编号：2024001
        甲方：北京科技有限公司
        乙方：上海贸易有限公司
        合同金额：100,000元
        签署日期：2024年1月15日
        项目内容：软件开发服务
        """

    @pytest.fixture
    def contract_schema(self):
        """合同提取Schema"""
        return {
            "type": "object",
            "properties": {
                "contract_number": {"type": "string", "description": "合同编号"},
                "party_a": {"type": "string", "description": "甲方"},
                "party_b": {"type": "string", "description": "乙方"},
                "amount": {"type": "number", "description": "合同金额"},
                "date": {"type": "string", "description": "签署日期"},
                "content": {"type": "string", "description": "项目内容"},
            },
            "required": ["contract_number", "party_a", "party_b"],
        }

    @pytest.mark.asyncio
    async def test_complete_extraction_workflow(
        self, config, sample_contract_text, contract_schema
    ):
        """测试完整的提取工作流"""
        extractor = DocumentExtractor(config)

        # Mock LLM响应
        mock_response = """{
            "contract_number": "2024001",
            "party_a": "北京科技有限公司",
            "party_b": "上海贸易有限公司", 
            "amount": 100000,
            "date": "2024年1月15日",
            "content": "软件开发服务"
        }"""

        with patch.object(
            extractor.llm_client, "extract_text", new_callable=AsyncMock
        ) as mock_llm:
            mock_llm.return_value = mock_response

            result = await extractor.extract(
                text_content=sample_contract_text, schema=contract_schema
            )

            # 验证结果
            assert result.status == ExtractionStatus.SUCCESS
            assert result.extracted_data is not None
            assert result.extracted_data["contract_number"] == "2024001"
            assert result.extracted_data["party_a"] == "北京科技有限公司"
            assert result.extracted_data["amount"] == 100000
            assert not result.has_errors()
            assert result.processing_time is not None
            assert result.processing_time > 0

    @pytest.mark.asyncio
    async def test_text_splitting_workflow(self, config, contract_schema):
        """测试文本分段工作流"""
        # 创建超长文本 - 确保足够长以触发分段
        long_text = "这是一个很长的合同文档内容。" * 500  # 确保超过max_chunk_size很多

        extractor = DocumentExtractor(config)

        with patch.object(
            extractor.llm_client, "extract_text", new_callable=AsyncMock
        ) as mock_llm:
            mock_llm.return_value = '{"contract_number": "2024001", "party_a": "测试公司", "party_b": "另一个公司"}'

            result = await extractor.extract(
                text_content=long_text, schema=contract_schema
            )

            # 验证分段处理
            assert "total_chunks" in result.metadata
            assert result.metadata["total_chunks"] > 1

            # 验证LLM被调用了多次（每个chunk一次）
            assert mock_llm.call_count >= result.metadata["total_chunks"]

    @pytest.mark.asyncio
    async def test_error_handling_and_retry(
        self, config, sample_contract_text, contract_schema
    ):
        """测试错误处理和重试"""
        extractor = DocumentExtractor(config)

        call_count = 0

        async def mock_llm_with_retry(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count < 3:  # 前两次调用失败
                raise Exception("API调用失败")
            return '{"contract_number": "2024001", "party_a": "测试公司", "party_b": "另一个公司"}'

        with patch.object(
            extractor.llm_client, "extract_text", new_callable=AsyncMock
        ) as mock_llm:
            mock_llm.side_effect = mock_llm_with_retry

            result = await extractor.extract(
                text_content=sample_contract_text, schema=contract_schema
            )

            # 验证重试机制工作
            assert call_count == 3  # 重试了2次后成功
            assert result.status == ExtractionStatus.SUCCESS

    @pytest.mark.asyncio
    async def test_json_parsing_error_handling(
        self, config, sample_contract_text, contract_schema
    ):
        """测试JSON解析错误处理"""
        extractor = DocumentExtractor(config)

        with patch.object(
            extractor.llm_client, "extract_text", new_callable=AsyncMock
        ) as mock_llm:
            mock_llm.return_value = "这不是有效的JSON格式"

            result = await extractor.extract(
                text_content=sample_contract_text, schema=contract_schema
            )

            assert result.status == ExtractionStatus.FAILED
            assert result.has_errors()
            assert any("JSON" in error.error_message for error in result.errors)

    @pytest.mark.asyncio
    async def test_schema_validation_error(
        self, config, sample_contract_text, contract_schema
    ):
        """测试Schema校验错误"""
        extractor = DocumentExtractor(config)

        # 返回不符合Schema的JSON
        invalid_response = '{"invalid_field": "value", "another_field": 123}'

        with patch.object(
            extractor.llm_client, "extract_text", new_callable=AsyncMock
        ) as mock_llm:
            mock_llm.return_value = invalid_response

            result = await extractor.extract(
                text_content=sample_contract_text, schema=contract_schema
            )

            assert result.status == ExtractionStatus.PARTIAL  # 有数据但有错误
            assert result.has_errors()

    def test_prompt_builder_functionality(self, config):
        """测试提示词构建功能"""
        from memect_insight_extractor.core.prompt_builder import PromptBuilder
        from memect_insight_extractor.models.extraction_request import ExtractionRequest

        builder = PromptBuilder()

        request = ExtractionRequest(
            text_content="测试文档内容",
            schema={"type": "object", "properties": {"name": {"type": "string"}}},
        )

        prompt = builder.build_prompt(request)

        # 验证提示词包含必要组件
        assert "世界级的信息提取专家" in prompt
        assert "JSON Schema" in prompt
        assert "测试文档内容" in prompt
        assert "type" in prompt and "object" in prompt

    def test_text_splitter_functionality(self, config):
        """测试文本分段功能"""
        from memect_insight_extractor.core.text_splitter import TextSplitter

        splitter = TextSplitter(config)

        # 测试短文本不分段
        short_text = "这是一个短文档"
        assert not splitter.should_split(short_text)

        chunks = splitter.split_text(short_text)
        assert len(chunks) == 1
        assert chunks[0].content == short_text

        # 测试长文本分段 - 确保足够长以触发分段
        long_text = "这是一个很长的文档内容。" * 500
        assert splitter.should_split(long_text)

        chunks = splitter.split_text(long_text)
        assert len(chunks) > 1

        # 验证所有块的总长度
        total_length = sum(len(chunk.content) for chunk in chunks)
        assert total_length >= len(long_text)  # 可能有重叠

    def test_result_parser_functionality(self):
        """测试结果解析功能"""
        from memect_insight_extractor.core.result_parser import ResultParser

        parser = ResultParser()

        # 测试有效JSON解析
        valid_json = '{"name": "测试", "age": 25}'
        schema = {
            "type": "object",
            "properties": {"name": {"type": "string"}, "age": {"type": "integer"}},
        }

        result, errors = parser.parse_and_validate(valid_json, schema)
        assert result is not None
        assert len(errors) == 0
        assert result["name"] == "测试"
        assert result["age"] == 25

        # 测试无效JSON
        invalid_json = "这不是JSON"
        result, errors = parser.parse_and_validate(invalid_json, schema)
        assert result is None
        assert len(errors) > 0

    def test_config_validation(self):
        """测试配置验证"""
        # 测试有效配置
        config = ExtractionConfig(llm_api_key="test-key")
        assert config.llm_api_key == "test-key"

        # 测试无效配置
        with pytest.raises(ValueError):
            ExtractionConfig(llm_api_key="test-key", llm_temperature=3.0)  # 超出范围

        with pytest.raises(ValueError):
            ExtractionConfig(llm_api_key="test-key", max_chunk_size=0)  # 无效值
