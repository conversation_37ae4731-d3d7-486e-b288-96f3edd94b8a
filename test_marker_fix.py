#!/usr/bin/env python3
"""测试marker修复的脚本"""

import sys
import os
sys.path.append('/Users/<USER>/Projects/memect_insight_extractor_preview')

from src.services.pdf_processor import PDFProcessor
import asyncio
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_marker_fix():
    """测试marker修复"""
    processor = PDFProcessor()

    # 测试模拟模式
    test_pdf_path = "test.pdf"

    try:
        logger.info("开始测试PDF转换（模拟模式）...")
        # 直接调用同步方法测试模拟模式
        markdown_content = processor._convert_pdf_sync(test_pdf_path)

        logger.info(f"转换成功！内容类型: {type(markdown_content)}")
        logger.info(f"内容长度: {len(markdown_content)} 字符")
        logger.info(f"内容预览: {markdown_content[:200]}...")

        # 检查是否是字符串类型
        if isinstance(markdown_content, str) and len(markdown_content) > 0:
            return True
        else:
            logger.error(f"返回内容类型错误: {type(markdown_content)}")
            return False

    except Exception as e:
        logger.error(f"转换失败: {str(e)}")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_marker_fix())
    if result:
        print("✅ 测试通过：marker修复成功")
    else:
        print("❌ 测试失败：marker修复有问题")
