#!/usr/bin/env python3
"""简单测试示例 - 验证模块导入和基本功能"""

import asyncio
from unittest.mock import AsyncMock, patch

from memect_insight_extractor import DocumentExtractor, ExtractionConfig


async def test_basic_functionality():
    """测试基本功能"""
    print("🚀 开始测试 Memect Insight Extractor...")
    
    # 1. 测试配置创建
    print("\n1. 测试配置创建...")
    config = ExtractionConfig(
        llm_api_key="test-key",
        llm_model="gpt-4",
        llm_temperature=0.1,
        max_chunk_tokens=500
    )
    print(f"✅ 配置创建成功: {config.llm_model}")
    
    # 2. 测试提取器创建
    print("\n2. 测试提取器创建...")
    extractor = DocumentExtractor(config)
    print("✅ 提取器创建成功")
    
    # 3. 测试Schema定义
    print("\n3. 测试Schema定义...")
    schema = {
        "type": "object",
        "properties": {
            "company_name": {"type": "string", "description": "公司名称"},
            "amount": {"type": "number", "description": "金额"},
            "date": {"type": "string", "description": "日期"}
        },
        "required": ["company_name"]
    }
    print("✅ Schema定义成功")
    
    # 4. 测试文档内容
    print("\n4. 准备测试文档...")
    text_content = """
    合同编号：2024001
    甲方：北京科技有限公司
    乙方：上海贸易有限公司
    合同金额：100,000元
    签署日期：2024年1月15日
    """
    print("✅ 测试文档准备完成")
    
    # 5. Mock LLM调用并测试提取
    print("\n5. 测试信息提取...")
    mock_response = '{"company_name": "北京科技有限公司", "amount": 100000, "date": "2024年1月15日"}'
    
    with patch.object(extractor.llm_client, 'extract_text', new_callable=AsyncMock) as mock_llm:
        mock_llm.return_value = mock_response
        
        result = await extractor.extract(
            text_content=text_content,
            schema=schema
        )
        
        print(f"✅ 提取完成，状态: {result.status}")
        print(f"✅ 处理时间: {result.processing_time:.3f}秒")
        
        if result.is_success():
            print("✅ 提取成功！")
            print(f"   公司名称: {result.extracted_data['company_name']}")
            print(f"   金额: {result.extracted_data['amount']}")
            print(f"   日期: {result.extracted_data['date']}")
        else:
            print("❌ 提取失败")
            for error in result.errors:
                print(f"   错误: {error.error_message}")
    
    # 6. 测试工具模块
    print("\n6. 测试工具模块...")
    from memect_insight_extractor.utils.validators import DataValidator
    
    # 测试邮箱验证
    assert DataValidator.validate_email("<EMAIL>") == True
    assert DataValidator.validate_email("invalid-email") == False
    print("✅ 邮箱验证功能正常")
    
    # 测试电话验证
    assert DataValidator.validate_phone("13812345678") == True
    assert DataValidator.validate_phone("123") == False
    print("✅ 电话验证功能正常")
    
    # 测试数字验证
    assert DataValidator.validate_number("123.45") == True
    assert DataValidator.validate_number("abc") == False
    print("✅ 数字验证功能正常")
    
    print("\n🎉 所有测试通过！Memect Insight Extractor 模块工作正常！")


def test_imports():
    """测试模块导入"""
    print("📦 测试模块导入...")
    
    try:
        from memect_insight_extractor import (
            DocumentExtractor,
            ExtractionConfig,
            ExtractionRequest,
            ExtractionResult,
        )
        print("✅ 主要类导入成功")
        
        from memect_insight_extractor.core import (
            PromptBuilder,
            TextSplitter,
            LLMClient,
            ResultParser,
            ErrorHandler,
        )
        print("✅ 核心模块导入成功")
        
        from memect_insight_extractor.utils import (
            get_logger,
            DataValidator,
            SchemaValidator,
        )
        print("✅ 工具模块导入成功")
        
        from memect_insight_extractor.models import (
            ExtractionError,
            SchemaField,
            ExtractionSchema,
        )
        print("✅ 数据模型导入成功")
        
        print("✅ 所有模块导入成功！")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    return True


if __name__ == "__main__":
    print("=" * 60)
    print("🧪 Memect Insight Extractor 模块测试")
    print("=" * 60)
    
    # 测试导入
    if test_imports():
        # 测试基本功能
        asyncio.run(test_basic_functionality())
    
    print("\n" + "=" * 60)
    print("✨ 测试完成！")
    print("=" * 60)
