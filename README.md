# Memect Insight Extractor Web Interface

基于 memect_insight_extractor 库的 Web 界面，提供 PDF 文档信息提取服务。

## 功能特性

- **配置管理**: 支持 OpenAI 兼容 API 的配置管理
- **PDF 处理**: 使用 marker 将 PDF 转换为 markdown 格式
- **异步提取**: 基于 memect_insight_extractor 的异步信息提取
- **任务管理**: SQLite 数据库存储任务状态和结果
- **Web 界面**: 纯静态前端，支持本地存储配置

## 项目结构

```
├── src/                    # 后端源码
│   ├── api/               # FastAPI 路由
│   ├── core/              # 核心业务逻辑
│   ├── models/            # 数据模型
│   ├── services/          # 服务层
│   └── utils/             # 工具函数
├── frontend/              # 前端静态文件
│   ├── index.html         # 主页面
│   ├── css/               # 样式文件
│   ├── js/                # JavaScript 文件
│   └── assets/            # 静态资源
├── tests/                 # 测试文件
├── test_data/             # 测试数据
└── docs/                  # 文档
```

## 快速开始

### 环境准备

```bash
# 1. 克隆项目（如果还没有）
git clone <repository-url>
cd memect_insight_extractor_preview

# 2. 创建虚拟环境（推荐使用指定的Python环境）
/opt/homebrew/Caskroom/miniforge/base/envs/memect_insight_extractor_preview/bin/python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 3. 运行自动安装脚本
python setup.py

# 或者手动安装
pip install -r requirements.txt
```

### 启动服务

```bash
# 方式1: 使用启动脚本（推荐）
python run.py

# 方式2: 直接使用uvicorn
uvicorn src.main:app --reload --host 0.0.0.0 --port 8000

# 访问服务
open http://localhost:8000
```

### 系统测试

```bash
# 运行系统测试确保一切正常
python test_system.py
```

## API 文档

启动服务后访问 http://localhost:8000/docs 查看 API 文档。

## 配置说明

系统支持以下配置项：

- **LLM API URL**: OpenAI 兼容的 API 基础 URL
- **API Key**: API 密钥
- **模型名称**: 使用的大语言模型名称
- **温度参数**: 控制输出随机性
- **最大 Token 数**: 单次请求的最大 token 限制

所有配置都会保存在浏览器的 localStorage 中，方便下次使用。

## 使用指南

### 1. 配置API

首次使用需要配置LLM API：

1. 点击导航栏的"配置管理"
2. 填写API基础URL（如：https://api.openai.com/v1）
3. 输入API密钥
4. 选择模型名称（如：gpt-4）
5. 调整其他参数（温度、最大token数等）
6. 点击"保存配置"

### 2. 提取文档信息

1. 点击导航栏的"文档提取"
2. 选择PDF文件（最大50MB）
3. 输入JSON Schema定义提取结构
4. （可选）配置高级选项：自定义提示词、处理指令
5. 点击"开始提取"
6. 系统会显示任务状态，自动轮询进度
7. 提取完成后可查看和下载结果

### 3. 查看历史记录

1. 点击导航栏的"历史记录"
2. 查看所有任务的状态和结果
3. 可以重新下载之前的提取结果
4. 任务ID会保存在localStorage中，刷新页面后仍可查看

### 4. Schema示例

项目提供了两个示例Schema文件：

- `test_data/sample_schema.json` - 合同信息提取
- `test_data/financial_schema.json` - 财务报告提取

可以参考这些示例来定义自己的提取结构。
