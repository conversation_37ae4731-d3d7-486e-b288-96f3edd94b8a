#!/opt/homebrew/Caskroom/miniforge/base/envs/memect_insight_extractor_preview/bin/python
"""启动脚本"""

import os
import sys
import uvicorn
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def main():
    """主函数"""
    # 检查Python版本
    if sys.version_info < (3, 8):
        logger.error("需要 Python 3.8 或更高版本")
        sys.exit(1)
    
    # 检查必要的目录
    os.makedirs("uploads", exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    os.makedirs("test_data", exist_ok=True)
    
    # 获取配置
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))
    reload = os.getenv("RELOAD", "true").lower() == "true"
    log_level = os.getenv("LOG_LEVEL", "info").lower()
    
    logger.info(f"启动服务器: {host}:{port}")
    logger.info(f"重载模式: {reload}")
    logger.info(f"日志级别: {log_level}")
    
    # 启动服务器
    uvicorn.run(
        "src.main:app",
        host=host,
        port=port,
        reload=reload,
        log_level=log_level,
        access_log=True
    )


if __name__ == "__main__":
    main()
