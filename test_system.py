#!/usr/bin/env python3
"""系统测试脚本"""

import asyncio
import json
import logging
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from src.core.database import init_database, get_db_session
from src.services.task_service import TaskService
from src.models.schemas import ExtractionConfigRequest

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_database():
    """测试数据库连接"""
    logger.info("测试数据库连接...")
    
    try:
        await init_database()
        logger.info("✅ 数据库初始化成功")
        
        # 测试数据库会话
        async for session in get_db_session():
            logger.info("✅ 数据库会话创建成功")
            break
            
    except Exception as e:
        logger.error(f"❌ 数据库测试失败: {e}")
        return False
    
    return True


async def test_task_service():
    """测试任务服务"""
    logger.info("测试任务服务...")
    
    try:
        task_service = TaskService()
        
        # 测试状态消息
        message = task_service._get_status_message("processing", 50.0)
        assert message == "处理中 (50.0%)"
        
        logger.info("✅ 任务服务测试成功")
        
    except Exception as e:
        logger.error(f"❌ 任务服务测试失败: {e}")
        return False
    
    return True


def test_config_validation():
    """测试配置验证"""
    logger.info("测试配置验证...")
    
    try:
        # 测试有效配置
        config = ExtractionConfigRequest(
            llm_base_url="https://api.openai.com/v1",
            llm_api_key="test-key",
            llm_model="gpt-4"
        )
        
        assert config.llm_base_url == "https://api.openai.com/v1"
        assert config.llm_temperature == 0.1  # 默认值
        
        logger.info("✅ 配置验证测试成功")
        
    except Exception as e:
        logger.error(f"❌ 配置验证测试失败: {e}")
        return False
    
    return True


def test_schema_loading():
    """测试Schema文件加载"""
    logger.info("测试Schema文件加载...")
    
    try:
        # 测试示例Schema文件
        schema_files = [
            "test_data/sample_schema.json",
            "test_data/financial_schema.json"
        ]
        
        for schema_file in schema_files:
            if os.path.exists(schema_file):
                with open(schema_file, 'r', encoding='utf-8') as f:
                    schema = json.load(f)
                    assert "type" in schema
                    assert "properties" in schema
                    logger.info(f"✅ Schema文件加载成功: {schema_file}")
            else:
                logger.warning(f"⚠️ Schema文件不存在: {schema_file}")
        
    except Exception as e:
        logger.error(f"❌ Schema文件测试失败: {e}")
        return False
    
    return True


def test_directory_structure():
    """测试目录结构"""
    logger.info("测试目录结构...")
    
    required_dirs = [
        "src",
        "src/api",
        "src/core",
        "src/models",
        "src/services",
        "frontend",
        "frontend/css",
        "frontend/js",
        "tests",
        "docs"
    ]
    
    required_files = [
        "src/main.py",
        "src/models/schemas.py",
        "src/models/database.py",
        "src/services/pdf_processor.py",
        "src/services/extraction_service.py",
        "src/services/task_service.py",
        "frontend/index.html",
        "frontend/css/style.css",
        "frontend/js/app.js",
        "requirements.txt",
        "README.md"
    ]
    
    try:
        # 检查目录
        for directory in required_dirs:
            if not os.path.exists(directory):
                logger.error(f"❌ 缺少目录: {directory}")
                return False
            logger.info(f"✅ 目录存在: {directory}")
        
        # 检查文件
        for file_path in required_files:
            if not os.path.exists(file_path):
                logger.error(f"❌ 缺少文件: {file_path}")
                return False
            logger.info(f"✅ 文件存在: {file_path}")
        
        logger.info("✅ 目录结构检查完成")
        
    except Exception as e:
        logger.error(f"❌ 目录结构测试失败: {e}")
        return False
    
    return True


async def main():
    """主测试函数"""
    logger.info("🚀 开始系统测试...")
    
    tests = [
        ("目录结构", test_directory_structure),
        ("配置验证", test_config_validation),
        ("Schema加载", test_schema_loading),
        ("数据库连接", test_database),
        ("任务服务", test_task_service),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 运行测试: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    logger.info(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！系统准备就绪。")
        logger.info("\n🚀 启动命令:")
        logger.info("   python run.py")
        logger.info("   或者: uvicorn src.main:app --reload --host 0.0.0.0 --port 8000")
        logger.info("\n🌐 访问地址:")
        logger.info("   Web界面: http://localhost:8000")
        logger.info("   API文档: http://localhost:8000/docs")
        return True
    else:
        logger.error("❌ 部分测试失败，请检查系统配置")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
