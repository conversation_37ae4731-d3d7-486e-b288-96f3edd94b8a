#!/usr/bin/env python3
"""测试完整上传流程的脚本"""

import requests
import json
import time
import os

def test_upload_flow():
    """测试完整的上传流程"""
    base_url = "http://localhost:8000"
    
    # 准备测试数据
    pdf_path = "/Users/<USER>/Projects/memect_insight_extractor_preview/uploads/001872_20230620_J7BC_1-30.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"❌ PDF文件不存在: {pdf_path}")
        return False
    
    # 准备提取请求
    extraction_request = {
        "config": {
            "llm_base_url": "https://api.openai.com/v1",
            "llm_api_key": "test-key",
            "llm_model": "gpt-3.5-turbo",
            "llm_temperature": 0.1,
            "llm_max_tokens": 4000,
            "llm_timeout": 30,
            "max_retries": 3,
            "retry_delay": 1,
            "max_chunk_size": 4000,
            "chunk_overlap": 200,
            "enable_parallel": False,
            "max_parallel_chunks": 3
        },
        "schema": {
            "type": "object",
            "properties": {
                "company_name": {"type": "string", "description": "公司名称"},
                "business_scope": {"type": "string", "description": "业务范围"}
            },
            "required": ["company_name"]
        },
        "prompt_template": None,
        "few_shot_examples": None,
        "custom_instructions": None
    }
    
    print("🚀 开始测试上传流程...")
    
    try:
        # 1. 上传PDF文件
        print("📤 上传PDF文件...")
        with open(pdf_path, 'rb') as f:
            files = {'file': ('test.pdf', f, 'application/pdf')}
            data = {'extraction_request': json.dumps(extraction_request)}
            
            response = requests.post(f"{base_url}/api/upload/pdf", files=files, data=data)
        
        if response.status_code != 200:
            print(f"❌ 上传失败: {response.status_code} - {response.text}")
            return False
        
        upload_result = response.json()
        task_id = upload_result['task_id']
        print(f"✅ 上传成功，任务ID: {task_id}")
        
        # 2. 轮询任务状态
        print("🔄 开始轮询任务状态...")
        max_polls = 60  # 最多轮询60次（2分钟）
        poll_count = 0
        
        while poll_count < max_polls:
            poll_count += 1
            
            # 获取任务状态
            status_response = requests.get(f"{base_url}/api/tasks/{task_id}/status")
            
            if status_response.status_code != 200:
                print(f"❌ 获取状态失败: {status_response.status_code}")
                return False
            
            status_data = status_response.json()
            status = status_data['status']
            progress = status_data.get('progress', 0)
            message = status_data.get('message', '')
            error_message = status_data.get('error_message')
            
            print(f"📊 轮询 {poll_count}: 状态={status}, 进度={progress:.1f}%, 消息={message}")
            
            if error_message:
                print(f"❌ 错误消息: {error_message}")
            
            # 检查任务是否完成
            if status == 'completed':
                print("✅ 任务完成！")
                
                # 获取任务结果
                result_response = requests.get(f"{base_url}/api/tasks/{task_id}/result")
                if result_response.status_code == 200:
                    result_data = result_response.json()
                    print(f"📄 Markdown内容长度: {len(result_data.get('markdown_content', ''))}")
                    print(f"📊 提取数据: {result_data.get('extracted_data', 'None')}")
                    print(f"⏱️ 处理时间: {result_data.get('processing_time', 0):.2f}秒")
                
                return True
                
            elif status == 'failed':
                print(f"❌ 任务失败: {error_message}")
                return False
            
            # 等待2秒后继续轮询
            time.sleep(2)
        
        print("⏰ 轮询超时，任务可能仍在处理中")
        return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_upload_flow()
    if success:
        print("\n🎉 完整上传流程测试通过！")
    else:
        print("\n💥 完整上传流程测试失败！")
