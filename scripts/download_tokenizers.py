#!/usr/bin/env python3
"""下载tokenizer文件，用于离线运行"""

import os
import json
import shutil
import tempfile
import requests
from pathlib import Path

def download_file(url: str, local_path: Path) -> bool:
    """下载文件到本地"""
    try:
        print(f"  下载: {url}")
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        local_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print(f"  ✅ 保存到: {local_path}")
        return True
        
    except Exception as e:
        print(f"  ❌ 下载失败: {e}")
        return False

def download_openai_tokenizers():
    """使用tiktoken下载OpenAI tokenizer文件到本地缓存"""
    print("📥 下载OpenAI tokenizer文件...")

    try:
        import tiktoken
    except ImportError:
        print("❌ tiktoken库不可用，请先安装: pip install tiktoken")
        return False

    project_root = Path(__file__).parent.parent
    cache_dir = project_root / "src" / "memect_insight_extractor" / "tokenizers" / "tiktoken_cache"
    cache_dir.mkdir(parents=True, exist_ok=True)

    # 需要下载的编码器
    encodings = ["cl100k_base", "p50k_base", "r50k_base", "o200k_base", "gpt2", "p50k_edit"]

    success_count = 0

    # 使用临时目录让tiktoken下载文件
    with tempfile.TemporaryDirectory() as temp_dir:
        # 设置临时缓存目录
        original_cache_dir = os.environ.get('TIKTOKEN_CACHE_DIR')
        os.environ['TIKTOKEN_CACHE_DIR'] = temp_dir

        try:
            for encoding_name in encodings:
                try:
                    print(f"  下载编码器: {encoding_name}")
                    # 让tiktoken下载编码器文件
                    encoding = tiktoken.get_encoding(encoding_name)

                    # 查找下载的文件并复制到项目目录
                    temp_cache_path = Path(temp_dir)
                    for file_path in temp_cache_path.glob("*"):
                        if file_path.is_file():
                            dest_path = cache_dir / file_path.name
                            shutil.copy2(file_path, dest_path)
                            print(f"  ✅ 复制文件: {file_path.name}")

                    success_count += 1

                except Exception as e:
                    print(f"  ❌ 下载 {encoding_name} 失败: {e}")

        finally:
            # 恢复原始缓存目录设置
            if original_cache_dir:
                os.environ['TIKTOKEN_CACHE_DIR'] = original_cache_dir
            elif 'TIKTOKEN_CACHE_DIR' in os.environ:
                del os.environ['TIKTOKEN_CACHE_DIR']

    # 创建元数据文件
    metadata = {
        "description": "OpenAI tokenizer cache files for offline use with tiktoken",
        "encodings": {
            "cl100k_base": {
                "models": ["gpt-4", "gpt-3.5-turbo", "text-embedding-ada-002"],
                "description": "Used by GPT-4, GPT-3.5-turbo, and text-embedding-ada-002"
            },
            "p50k_base": {
                "models": ["text-davinci-003", "text-davinci-002"],
                "description": "Used by text-davinci-003 and text-davinci-002"
            },
            "r50k_base": {
                "models": ["text-davinci-001", "davinci"],
                "description": "Used by text-davinci-001 and davinci"
            },
            "o200k_base": {
                "models": ["gpt-4o", "o1", "o3"],
                "description": "Used by GPT-4o, o1, and o3"
            },
            "gpt2": {
                "models": ["gpt-2"],
                "description": "Used by GPT-2"
            },
            "p50k_edit": {
                "models": ["text-davinci-edit-001", "code-davinci-edit-001"],
                "description": "Used by edit models"
            }
        }
    }

    metadata_file = cache_dir / "metadata.json"
    with open(metadata_file, 'w', encoding='utf-8') as f:
        json.dump(metadata, f, indent=2, ensure_ascii=False)

    print(f"✅ OpenAI tokenizer下载完成: {success_count}/{len(encodings)} 个编码器")
    return success_count > 0

def download_qwen_tokenizers():
    """下载不同版本的Qwen tokenizer文件"""
    print("📥 下载Qwen tokenizer文件...")

    project_root = Path(__file__).parent.parent

    # 不同版本的Qwen tokenizer
    qwen_versions = {
        "2": {
            "base_url": "https://huggingface.co/Qwen/Qwen2-7B-Instruct/resolve/main",
            "description": "Qwen2 tokenizer files"
        },
        "2.5": {
            "base_url": "https://huggingface.co/Qwen/Qwen2.5-7B-Instruct/resolve/main",
            "description": "Qwen2.5 tokenizer files"
        },
        "3": {
            "base_url": "https://huggingface.co/Qwen/Qwen3-8B/resolve/main",
            "description": "Qwen3 tokenizer files"
        }
    }

    # 需要下载的文件
    files = ["tokenizer.json", "tokenizer_config.json", "vocab.json", "merges.txt"]

    total_success = 0

    for version, config in qwen_versions.items():
        print(f"  下载Qwen{version} tokenizer...")
        tokenizer_dir = project_root / "src" / "memect_insight_extractor" / "tokenizers" / f"qwen{version}"

        success_count = 0
        for filename in files:
            url = f"{config['base_url']}/{filename}"
            local_path = tokenizer_dir / filename
            if download_file(url, local_path):
                success_count += 1

        # 创建元数据文件
        metadata = {
            "description": config["description"],
            "version": version,
            "model_name": f"Qwen{version}-7B-Instruct",
            "files": files
        }

        metadata_file = tokenizer_dir / "metadata.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)

        if success_count > 0:
            total_success += 1
            print(f"  ✅ Qwen{version} tokenizer下载完成: {success_count}/{len(files)} 个文件")
        else:
            print(f"  ❌ Qwen{version} tokenizer下载失败")

    print(f"✅ Qwen tokenizer下载完成: {total_success}/{len(qwen_versions)} 个版本")
    return total_success > 0

def create_init_files():
    """创建必要的__init__.py文件"""
    project_root = Path(__file__).parent.parent
    tokenizers_dir = project_root / "src" / "memect_insight_extractor" / "tokenizers"

    # 创建目录结构
    subdirs = ["", "tiktoken_cache", "qwen2", "qwen2.5", "qwen3"]

    for subdir in subdirs:
        dir_path = tokenizers_dir / subdir if subdir else tokenizers_dir
        dir_path.mkdir(parents=True, exist_ok=True)

        init_file = dir_path / "__init__.py"
        with open(init_file, 'w', encoding='utf-8') as f:
            if subdir == "tiktoken_cache":
                f.write('"""Tiktoken缓存文件"""\n')
            elif subdir.startswith("qwen"):
                f.write(f'"""{subdir.upper()} tokenizer文件"""\n')
            else:
                f.write('"""Tokenizer资源文件"""\n')

def main():
    """主函数"""
    print("🚀 开始下载tokenizer文件...")

    # 创建目录结构
    create_init_files()

    success_count = 0

    # 下载OpenAI tokenizer
    if download_openai_tokenizers():
        success_count += 1

    # 下载Qwen tokenizer
    if download_qwen_tokenizers():
        success_count += 1

    print(f"\n🎉 下载完成！成功下载 {success_count} 个tokenizer类型")

    if success_count == 0:
        print("⚠️ 没有成功下载任何tokenizer文件")
        return False

    # 显示文件结构
    project_root = Path(__file__).parent.parent
    tokenizers_dir = project_root / "src" / "memect_insight_extractor" / "tokenizers"

    print(f"\n📦 文件结构:")
    for root, dirs, files in os.walk(tokenizers_dir):
        level = root.replace(str(tokenizers_dir), '').count(os.sep)
        indent = '  ' * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = '  ' * (level + 1)
        for file in files:
            if not file.startswith('.'):
                print(f"{subindent}{file}")

    return True

if __name__ == "__main__":
    main()
