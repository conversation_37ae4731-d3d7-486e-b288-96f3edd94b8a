#!/usr/bin/env python3
"""安装和设置脚本"""

import os
import sys
import subprocess
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def check_python_version():
    """检查Python版本"""
    logger.info("检查Python版本...")
    
    if sys.version_info < (3, 8):
        logger.error("需要Python 3.8或更高版本")
        logger.error(f"当前版本: {sys.version}")
        return False
    
    logger.info(f"✅ Python版本: {sys.version}")
    return True


def create_directories():
    """创建必要的目录"""
    logger.info("创建必要的目录...")
    
    directories = [
        "uploads",
        "logs",
        "test_data",
        "temp"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"✅ 创建目录: {directory}")


def install_dependencies():
    """安装依赖包"""
    logger.info("安装依赖包...")
    
    try:
        # 检查是否在虚拟环境中
        if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
            logger.info("✅ 检测到虚拟环境")
        else:
            logger.warning("⚠️ 建议在虚拟环境中运行")
        
        # 安装requirements.txt中的依赖
        if os.path.exists("requirements.txt"):
            logger.info("安装requirements.txt中的依赖...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ])
            logger.info("✅ 依赖安装完成")
        else:
            logger.error("❌ requirements.txt文件不存在")
            return False
        
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ 依赖安装失败: {e}")
        return False


def setup_memect_library():
    """设置memect_insight_extractor库"""
    logger.info("设置memect_insight_extractor库...")
    
    memect_dir = "memect-insight-extractor"
    
    if not os.path.exists(memect_dir):
        logger.error(f"❌ 找不到{memect_dir}目录")
        logger.error("请确保memect-insight-extractor库在项目根目录下")
        return False
    
    # 检查库的结构
    required_paths = [
        f"{memect_dir}/src",
        f"{memect_dir}/src/memect_insight_extractor",
        f"{memect_dir}/src/memect_insight_extractor/__init__.py"
    ]
    
    for path in required_paths:
        if not os.path.exists(path):
            logger.error(f"❌ 缺少路径: {path}")
            return False
    
    logger.info("✅ memect_insight_extractor库结构正确")
    return True


def create_env_example():
    """创建环境变量示例文件"""
    logger.info("创建环境变量示例文件...")
    
    env_content = """# 环境变量配置示例
# 复制此文件为 .env 并修改相应的值

# 服务器配置
HOST=0.0.0.0
PORT=8000
RELOAD=true
LOG_LEVEL=info

# 数据库配置
DATABASE_URL=sqlite+aiosqlite:///./tasks.db

# 文件上传配置
MAX_FILE_SIZE=52428800  # 50MB
UPLOAD_DIR=uploads

# 日志配置
LOG_DIR=logs
"""
    
    with open(".env.example", "w", encoding="utf-8") as f:
        f.write(env_content)
    
    logger.info("✅ 创建.env.example文件")


def run_tests():
    """运行系统测试"""
    logger.info("运行系统测试...")
    
    try:
        result = subprocess.run([
            sys.executable, "test_system.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ 系统测试通过")
            return True
        else:
            logger.error("❌ 系统测试失败")
            logger.error(result.stderr)
            return False
            
    except Exception as e:
        logger.error(f"❌ 运行测试失败: {e}")
        return False


def main():
    """主安装函数"""
    logger.info("🚀 开始安装和设置...")
    
    steps = [
        ("检查Python版本", check_python_version),
        ("创建目录", create_directories),
        ("安装依赖", install_dependencies),
        ("设置memect库", setup_memect_library),
        ("创建环境配置", create_env_example),
        ("运行测试", run_tests),
    ]
    
    for step_name, step_func in steps:
        logger.info(f"\n📋 执行步骤: {step_name}")
        
        try:
            if not step_func():
                logger.error(f"❌ 步骤失败: {step_name}")
                return False
        except Exception as e:
            logger.error(f"❌ 步骤异常: {step_name} - {e}")
            return False
    
    logger.info("\n🎉 安装和设置完成！")
    logger.info("\n📋 下一步:")
    logger.info("1. 配置环境变量（可选）: cp .env.example .env")
    logger.info("2. 启动服务: python run.py")
    logger.info("3. 访问Web界面: http://localhost:8000")
    logger.info("4. 查看API文档: http://localhost:8000/docs")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
