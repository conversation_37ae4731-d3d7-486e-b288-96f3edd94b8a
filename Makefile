# Makefile for Memect Insight Extractor Web Interface

.PHONY: help setup install test run clean dev docs

# 默认目标
help:
	@echo "可用的命令:"
	@echo "  setup     - 运行完整的安装和设置"
	@echo "  install   - 安装Python依赖"
	@echo "  test      - 运行系统测试"
	@echo "  run       - 启动开发服务器"
	@echo "  dev       - 启动开发模式（自动重载）"
	@echo "  clean     - 清理临时文件"
	@echo "  docs      - 生成文档"

# 完整安装和设置
setup:
	@echo "🚀 开始完整安装和设置..."
	python setup.py

# 安装依赖
install:
	@echo "📦 安装Python依赖..."
	pip install -r requirements.txt

# 运行测试
test:
	@echo "🧪 运行系统测试..."
	python test_system.py

# 启动生产服务器
run:
	@echo "🚀 启动服务器..."
	python run.py

# 启动开发服务器
dev:
	@echo "🔧 启动开发服务器（自动重载）..."
	uvicorn src.main:app --reload --host 0.0.0.0 --port 8000

# 清理临时文件
clean:
	@echo "🧹 清理临时文件..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	rm -rf .pytest_cache
	rm -rf *.egg-info
	rm -rf build/
	rm -rf dist/
	rm -rf temp/
	rm -f *.db
	rm -f *.sqlite
	rm -f *.sqlite3

# 生成文档
docs:
	@echo "📚 生成文档..."
	@echo "文档位于 docs/ 目录"
	@echo "主要文档文件:"
	@echo "  - docs/index.md"
	@echo "  - README.md"

# 检查代码质量
lint:
	@echo "🔍 检查代码质量..."
	python -m flake8 src/ --max-line-length=100 --ignore=E203,W503
	python -m black --check src/

# 格式化代码
format:
	@echo "✨ 格式化代码..."
	python -m black src/
	python -m isort src/

# 创建虚拟环境
venv:
	@echo "🐍 创建虚拟环境..."
	python -m venv venv
	@echo "激活虚拟环境: source venv/bin/activate"

# 检查依赖
check-deps:
	@echo "🔍 检查依赖..."
	pip check
	pip list --outdated

# 更新依赖
update-deps:
	@echo "⬆️ 更新依赖..."
	pip install --upgrade pip
	pip install --upgrade -r requirements.txt

# 备份数据库
backup-db:
	@echo "💾 备份数据库..."
	@if [ -f "tasks.db" ]; then \
		cp tasks.db "tasks_backup_$(shell date +%Y%m%d_%H%M%S).db"; \
		echo "数据库已备份"; \
	else \
		echo "数据库文件不存在"; \
	fi

# 重置数据库
reset-db:
	@echo "🗑️ 重置数据库..."
	@if [ -f "tasks.db" ]; then \
		rm tasks.db; \
		echo "数据库已删除"; \
	else \
		echo "数据库文件不存在"; \
	fi

# 显示项目信息
info:
	@echo "📋 项目信息:"
	@echo "  项目名称: Memect Insight Extractor Web Interface"
	@echo "  Python版本: $(shell python --version)"
	@echo "  项目路径: $(shell pwd)"
	@echo "  虚拟环境: $(shell echo $$VIRTUAL_ENV)"
	@echo "  依赖数量: $(shell pip list | wc -l)"

# 快速启动（包含测试）
quick-start: test run

# 完整重置
reset: clean reset-db
	@echo "🔄 项目已重置"
