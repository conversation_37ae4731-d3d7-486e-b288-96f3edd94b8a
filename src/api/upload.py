"""文件上传API"""

import os
import logging
from typing import Annotated
from fastapi import APIRouter, UploadFile, File, Form, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.database import get_db_session
from src.services.pdf_processor import PDFProcessor
from src.services.task_service import TaskService
from src.services.extraction_service import ExtractionService
from src.models.schemas import (
    UploadResponse,
    ExtractionRequest,
    ErrorResponse,
    ExtractionConfigRequest,
    TaskStatus
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/upload", tags=["upload"])

# 服务实例
pdf_processor = PDFProcessor()
task_service = TaskService()
extraction_service = ExtractionService()


@router.post("/pdf", response_model=UploadResponse)
async def upload_pdf(
    file: UploadFile = File(...),
    extraction_request: str = Form(...),
    db_session: AsyncSession = Depends(get_db_session)
):
    """上传PDF文件并启动提取任务
    
    Args:
        file: 上传的PDF文件
        extraction_request: JSON格式的提取请求
        db_session: 数据库会话
        
    Returns:
        上传响应
    """
    try:
        # 验证文件类型
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(
                status_code=400,
                detail="只支持PDF文件"
            )
        
        # 验证文件大小 (50MB限制)
        file_content = await file.read()
        if len(file_content) > 50 * 1024 * 1024:
            raise HTTPException(
                status_code=400,
                detail="文件大小不能超过50MB"
            )
        
        # 解析提取请求
        import json
        try:
            request_data = json.loads(extraction_request)
            extraction_req = ExtractionRequest(**request_data)
        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail=f"提取请求格式错误: {str(e)}"
            )
        
        # 保存上传的文件
        file_path = await pdf_processor.save_uploaded_file(
            file_content, file.filename
        )
        
        # 创建任务
        task_id = await task_service.create_task(
            db_session=db_session,
            filename=file.filename,
            file_size=len(file_content),
            file_path=file_path,
            extraction_config=extraction_req.config.dict(),
            extraction_schema=extraction_req.schema,
            prompt_template=extraction_req.prompt_template,
            few_shot_examples=extraction_req.few_shot_examples,
            custom_instructions=extraction_req.custom_instructions
        )
        
        # 启动PDF转换和提取任务
        await _start_processing_task(
            task_id, file_path, extraction_req, db_session
        )
        
        logger.info(f"PDF上传成功: {file.filename}, 任务ID: {task_id}")
        
        return UploadResponse(
            task_id=task_id,
            filename=file.filename,
            file_size=len(file_content),
            message="文件上传成功，开始处理"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"PDF上传失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"文件上传失败: {str(e)}"
        )


async def _start_processing_task(
    task_id: str,
    file_path: str,
    extraction_request: ExtractionRequest,
    db_session: AsyncSession
):
    """启动处理任务
    
    Args:
        task_id: 任务ID
        file_path: 文件路径
        extraction_request: 提取请求
        db_session: 数据库会话
    """
    import asyncio
    
    async def process():
        try:
            # 转换PDF为Markdown
            markdown_content = await pdf_processor.convert_pdf_to_markdown(file_path)
            
            # 保存Markdown内容到数据库
            await task_service.update_task_markdown(
                db_session, task_id, markdown_content
            )
            
            # 启动信息提取任务
            await extraction_service.start_extraction_task(
                task_id=task_id,
                markdown_content=markdown_content,
                schema=extraction_request.schema,
                config=extraction_request.config,
                db_session=db_session,
                prompt_template=extraction_request.prompt_template,
                few_shot_examples=extraction_request.few_shot_examples,
                custom_instructions=extraction_request.custom_instructions
            )
            
        except Exception as e:
            logger.error(f"处理任务失败: {task_id}, 错误: {str(e)}")
            # 更新任务状态为失败
            try:
                await task_service.update_task_status(
                    db_session, task_id, TaskStatus.FAILED,
                    progress=0.0, error_message=str(e)
                )
            except Exception as update_error:
                logger.error(f"更新任务状态失败: {task_id}, 错误: {str(update_error)}")
        finally:
            # 清理临时文件
            pdf_processor.cleanup_file(file_path)
    
    # 创建后台任务
    asyncio.create_task(process())


@router.get("/limits")
async def get_upload_limits():
    """获取上传限制信息"""
    return {
        "max_file_size": 50 * 1024 * 1024,  # 50MB
        "allowed_types": [".pdf"],
        "max_file_size_mb": 50
    }
