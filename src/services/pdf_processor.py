"""PDF处理服务"""

import os
import tempfile
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Optional
import aiofiles
import logging

# marker库导入
try:
    from marker.converters.pdf import PdfConverter
    from marker.models import create_model_dict
    MARKER_AVAILABLE = True
except ImportError as e:
    logging.warning(f"marker库不可用: {e}")
    logging.warning("PDF转换功能将使用模拟模式")
    MARKER_AVAILABLE = False

logger = logging.getLogger(__name__)


class PDFProcessor:
    """PDF处理器"""
    
    def __init__(self, upload_dir: str = "uploads"):
        """初始化PDF处理器
        
        Args:
            upload_dir: 上传文件目录
        """
        self.upload_dir = Path(upload_dir)
        self.upload_dir.mkdir(exist_ok=True)
    
    async def save_uploaded_file(self, file_content: bytes, filename: str) -> str:
        """保存上传的文件
        
        Args:
            file_content: 文件内容
            filename: 文件名
            
        Returns:
            保存的文件路径
        """
        file_path = self.upload_dir / filename
        
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(file_content)
        
        logger.info(f"文件已保存: {file_path}")
        return str(file_path)
    
    async def convert_pdf_to_markdown(self, pdf_path: str) -> str:
        """将PDF转换为Markdown

        Args:
            pdf_path: PDF文件路径

        Returns:
            转换后的Markdown内容

        Raises:
            Exception: 转换失败时抛出异常
        """
        try:
            logger.info(f"开始转换PDF: {pdf_path}")

            # 使用线程池运行同步的marker转换函数
            loop = asyncio.get_event_loop()
            markdown_content = await loop.run_in_executor(
                None,
                self._convert_pdf_sync,
                pdf_path
            )

            logger.info(f"PDF转换完成，内容长度: {len(markdown_content)} 字符")
            return markdown_content

        except Exception as e:
            logger.error(f"PDF转换失败: {str(e)}")
            raise Exception(f"PDF转换失败: {str(e)}")

    def _convert_pdf_sync(self, pdf_path: str) -> str:
        """同步转换PDF（在线程池中运行）"""
        if not MARKER_AVAILABLE:
            # 模拟模式：返回示例内容
            logger.warning("使用PDF转换模拟模式")
            return f"""# PDF转换结果（模拟模式）

**文件路径**: {pdf_path}
**转换时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 文档内容

这是一个模拟的PDF转换结果。在实际使用中，这里会包含从PDF文件中提取的真实内容。

### 示例章节

1. **第一章**: 介绍内容
2. **第二章**: 详细说明
3. **第三章**: 总结

### 表格示例

| 项目 | 数值 | 说明 |
|------|------|------|
| 收入 | 1000万 | 年度收入 |
| 支出 | 800万 | 年度支出 |
| 利润 | 200万 | 净利润 |

**注意**: 这是模拟内容，请安装完整的marker库以获得真实的PDF转换功能。
"""

        try:
            # 创建模型字典
            model_dict = create_model_dict()

            # 创建PDF转换器
            converter = PdfConverter(
                artifact_dict=model_dict,
                processor_list=None,  # 使用默认处理器
                renderer=None,  # 使用默认Markdown渲染器
                config={"use_llm": False}  # 不使用LLM以提高速度
            )

            # 执行转换
            rendered_output = converter(pdf_path)

            # 从MarkdownOutput对象中提取markdown内容
            if hasattr(rendered_output, 'markdown'):
                markdown_content = rendered_output.markdown
            else:
                # 如果返回的是字符串，直接使用
                markdown_content = str(rendered_output)

            return markdown_content

        except Exception as e:
            logger.error(f"同步PDF转换失败: {str(e)}")
            raise
    
    def cleanup_file(self, file_path: str) -> None:
        """清理临时文件
        
        Args:
            file_path: 文件路径
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"已清理文件: {file_path}")
        except Exception as e:
            logger.warning(f"清理文件失败: {file_path}, 错误: {str(e)}")
    
    def get_file_info(self, file_path: str) -> dict:
        """获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        try:
            stat = os.stat(file_path)
            return {
                "size": stat.st_size,
                "created_at": stat.st_ctime,
                "modified_at": stat.st_mtime
            }
        except Exception as e:
            logger.warning(f"获取文件信息失败: {file_path}, 错误: {str(e)}")
            return {}
