"""任务管理服务"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc
from src.models.database import Task
from src.models.schemas import TaskStatus, TaskResponse, TaskResult

import logging

logger = logging.getLogger(__name__)


class TaskService:
    """任务管理服务"""
    
    async def create_task(
        self,
        db_session: AsyncSession,
        filename: str,
        file_size: int,
        file_path: str,
        extraction_config: Dict[str, Any],
        extraction_schema: Dict[str, Any],
        prompt_template: Optional[str] = None,
        few_shot_examples: Optional[List[Dict[str, Any]]] = None,
        custom_instructions: Optional[str] = None
    ) -> str:
        """创建新任务
        
        Args:
            db_session: 数据库会话
            filename: 文件名
            file_size: 文件大小
            file_path: 文件路径
            extraction_config: 提取配置
            extraction_schema: 提取Schema
            prompt_template: 自定义提示词模板
            few_shot_examples: 少样本示例
            custom_instructions: 自定义指令
            
        Returns:
            任务ID
        """
        task_id = str(uuid.uuid4())
        
        task = Task(
            task_id=task_id,
            status=TaskStatus.PENDING.value,
            filename=filename,
            file_size=file_size,
            file_path=file_path,
            extraction_config=extraction_config,
            extraction_schema=extraction_schema,
            prompt_template=prompt_template,
            few_shot_examples=few_shot_examples,
            custom_instructions=custom_instructions,
            progress=0.0
        )
        
        db_session.add(task)
        await db_session.commit()
        await db_session.refresh(task)
        
        logger.info(f"任务已创建: {task_id}")
        return task_id
    
    async def get_task(self, db_session: AsyncSession, task_id: str) -> Optional[Task]:
        """获取任务信息
        
        Args:
            db_session: 数据库会话
            task_id: 任务ID
            
        Returns:
            任务对象或None
        """
        stmt = select(Task).where(Task.task_id == task_id)
        result = await db_session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_task_status(self, db_session: AsyncSession, task_id: str) -> Optional[TaskResponse]:
        """获取任务状态
        
        Args:
            db_session: 数据库会话
            task_id: 任务ID
            
        Returns:
            任务状态响应或None
        """
        task = await self.get_task(db_session, task_id)
        if not task:
            return None
        
        return TaskResponse(
            task_id=task.task_id,
            status=TaskStatus(task.status),
            created_at=task.created_at,
            updated_at=task.updated_at,
            filename=task.filename,
            file_size=task.file_size,
            progress=task.progress,
            message=self._get_status_message(task.status, task.progress),
            error_message=task.error_message
        )
    
    async def get_task_result(self, db_session: AsyncSession, task_id: str) -> Optional[TaskResult]:
        """获取任务结果
        
        Args:
            db_session: 数据库会话
            task_id: 任务ID
            
        Returns:
            任务结果或None
        """
        task = await self.get_task(db_session, task_id)
        if not task:
            return None
        
        return TaskResult(
            task_id=task.task_id,
            status=TaskStatus(task.status),
            extracted_data=task.extracted_data,
            processing_time=task.processing_time,
            markdown_content=task.markdown_content,
            error_message=task.error_message,
            metadata=task.task_metadata
        )
    
    async def get_recent_tasks(
        self, 
        db_session: AsyncSession, 
        limit: int = 20
    ) -> List[TaskResponse]:
        """获取最近的任务列表
        
        Args:
            db_session: 数据库会话
            limit: 返回数量限制
            
        Returns:
            任务列表
        """
        stmt = select(Task).order_by(desc(Task.created_at)).limit(limit)
        result = await db_session.execute(stmt)
        tasks = result.scalars().all()
        
        return [
            TaskResponse(
                task_id=task.task_id,
                status=TaskStatus(task.status),
                created_at=task.created_at,
                updated_at=task.updated_at,
                filename=task.filename,
                file_size=task.file_size,
                progress=task.progress,
                message=self._get_status_message(task.status, task.progress),
                error_message=task.error_message
            )
            for task in tasks
        ]
    
    async def update_task_markdown(
        self, 
        db_session: AsyncSession, 
        task_id: str, 
        markdown_content: str
    ) -> bool:
        """更新任务的Markdown内容
        
        Args:
            db_session: 数据库会话
            task_id: 任务ID
            markdown_content: Markdown内容
            
        Returns:
            是否更新成功
        """
        task = await self.get_task(db_session, task_id)
        if not task:
            return False
        
        task.markdown_content = markdown_content
        await db_session.commit()
        
        logger.info(f"任务Markdown内容已更新: {task_id}")
        return True
    
    def _get_status_message(self, status: str, progress: float) -> str:
        """获取状态消息
        
        Args:
            status: 任务状态
            progress: 进度
            
        Returns:
            状态消息
        """
        status_messages = {
            TaskStatus.PENDING.value: "等待处理",
            TaskStatus.PROCESSING.value: f"处理中 ({progress:.1f}%)",
            TaskStatus.COMPLETED.value: "处理完成",
            TaskStatus.FAILED.value: "处理失败"
        }
        
        return status_messages.get(status, "未知状态")
