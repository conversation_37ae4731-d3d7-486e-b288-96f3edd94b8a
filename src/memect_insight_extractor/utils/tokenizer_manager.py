"""本地tokenizer管理器，使用tiktoken离线模式实现token计算"""

import os
import json
from pathlib import Path
from typing import Optional, Dict

from ..utils.logger import get_logger

logger = get_logger(__name__)


class LocalTokenizerManager:
    """本地tokenizer管理器，使用tiktoken离线模式"""

    def __init__(self):
        self.package_dir = Path(__file__).parent.parent
        self.tokenizers_dir = self.package_dir / "tokenizers"
        self.tiktoken_cache_dir = self.tokenizers_dir / "tiktoken_cache"
        self._tokenizers = {}
        self._setup_tiktoken_cache()

        # OpenAI模型到编码的映射（来自tiktoken官方）
        self.model_to_encoding = {
            # reasoning
            "o1": "o200k_base",
            "o3": "o200k_base",
            # chat
            "gpt-4o": "o200k_base",
            "gpt-4": "cl100k_base",
            "gpt-4.1": "o200k_base",
            "gpt-4.5": "o200k_base",
            "gpt-3.5-turbo": "cl100k_base",
            "gpt-3.5": "cl100k_base",
            "gpt-35-turbo": "cl100k_base",  # Azure deployment name
            # base
            "davinci-002": "cl100k_base",
            "babbage-002": "cl100k_base",
            # embeddings
            "text-embedding-ada-002": "cl100k_base",
            "text-embedding-3-small": "cl100k_base",
            "text-embedding-3-large": "cl100k_base",
            # DEPRECATED MODELS
            "text-davinci-003": "p50k_base",
            "text-davinci-002": "p50k_base",
            "text-davinci-001": "r50k_base",
            "text-curie-001": "r50k_base",
            "text-babbage-001": "r50k_base",
            "text-ada-001": "r50k_base",
            "davinci": "r50k_base",
            "curie": "r50k_base",
            "babbage": "r50k_base",
            "ada": "r50k_base",
            "code-davinci-002": "p50k_base",
            "code-davinci-001": "p50k_base",
            "code-cushman-002": "p50k_base",
            "code-cushman-001": "p50k_base",
            "davinci-codex": "p50k_base",
            "cushman-codex": "p50k_base",
            "text-davinci-edit-001": "p50k_edit",
            "code-davinci-edit-001": "p50k_edit",
            "gpt2": "gpt2",
            "gpt-2": "gpt2",
        }

        # 模型前缀到编码的映射
        self.model_prefix_to_encoding = {
            "o1-": "o200k_base",
            "o3-": "o200k_base",
            "chatgpt-4o-": "o200k_base",
            "gpt-4o-": "o200k_base",
            "gpt-4-": "cl100k_base",
            "gpt-4.1-": "o200k_base",
            "gpt-4.5-": "o200k_base",
            "gpt-3.5-turbo-": "cl100k_base",
            "gpt-35-turbo-": "cl100k_base",
            "ft:gpt-4o": "o200k_base",
            "ft:gpt-4": "cl100k_base",
            "ft:gpt-3.5-turbo": "cl100k_base",
            "ft:davinci-002": "cl100k_base",
            "ft:babbage-002": "cl100k_base",
        }
    
    def _setup_tiktoken_cache(self) -> None:
        """设置tiktoken缓存目录"""
        if self.tiktoken_cache_dir.exists():
            # 动态设置tiktoken缓存目录
            os.environ['TIKTOKEN_CACHE_DIR'] = str(self.tiktoken_cache_dir)
            logger.debug(f"设置TIKTOKEN_CACHE_DIR: {self.tiktoken_cache_dir}")

    def get_tokenizer(self, tokenizer_type: str = "openai", model_name: str = "gpt-4"):
        """获取tokenizer实例

        Args:
            tokenizer_type: tokenizer类型 ("openai" 或 "qwen")
            model_name: 模型名称

        Returns:
            tokenizer实例或None
        """
        cache_key = f"{tokenizer_type}_{model_name}"

        if cache_key in self._tokenizers:
            return self._tokenizers[cache_key]

        try:
            if tokenizer_type.lower() == "openai":
                tokenizer = self._load_openai_tokenizer(model_name)
            elif tokenizer_type.lower() == "qwen":
                tokenizer = self._load_qwen_tokenizer(model_name)
            else:
                logger.warning(f"不支持的tokenizer类型: {tokenizer_type}")
                return None

            if tokenizer:
                self._tokenizers[cache_key] = tokenizer
                logger.info(f"成功加载 {tokenizer_type} tokenizer for {model_name}")

            return tokenizer

        except Exception as e:
            logger.error(f"加载tokenizer失败: {e}")
            return None
    
    def _load_openai_tokenizer(self, model_name: str):
        """加载OpenAI tokenizer，使用tiktoken离线模式

        Args:
            model_name: 模型名称

        Returns:
            tokenizer实例或None
        """
        try:
            import tiktoken

            encoding_name = self._get_openai_encoding_name(model_name)

            # tiktoken会使用TIKTOKEN_CACHE_DIR环境变量指定的缓存目录
            tokenizer = tiktoken.get_encoding(encoding_name)
            logger.debug(f"使用tiktoken获取编码器: {encoding_name} for model: {model_name}")
            return tokenizer

        except ImportError:
            logger.warning("tiktoken库不可用，无法使用OpenAI tokenizer")
            return None
        except Exception as e:
            logger.error(f"加载OpenAI tokenizer失败: {e}")
            return None
    
    def _load_qwen_tokenizer(self, model_name: str = "qwen2"):
        """加载Qwen tokenizer，支持不同版本

        Args:
            model_name: 模型名称，用于区分Qwen版本

        Returns:
            tokenizer实例或None
        """
        try:
            from tokenizers import Tokenizer

            # 根据模型名称确定Qwen版本
            qwen_version = self._get_qwen_version(model_name)
            qwen_dir = self.tokenizers_dir / f"qwen{qwen_version}"
            tokenizer_file = qwen_dir / "tokenizer.json"

            if not tokenizer_file.exists():
                logger.warning(f"Qwen{qwen_version} tokenizer文件不存在: {tokenizer_file}")
                return None

            tokenizer = Tokenizer.from_file(str(tokenizer_file))
            logger.debug(f"加载Qwen{qwen_version} tokenizer")
            return tokenizer

        except ImportError:
            logger.warning("tokenizers库不可用，无法使用Qwen tokenizer")
            return None
        except Exception as e:
            logger.error(f"加载Qwen tokenizer失败: {e}")
            return None

    def _get_qwen_version(self, model_name: str) -> str:
        """根据模型名称确定Qwen版本

        Args:
            model_name: 模型名称

        Returns:
            Qwen版本字符串
        """
        model_name_lower = model_name.lower()

        if "qwen3" in model_name_lower:
            return "3"
        elif "qwen2.5" in model_name_lower:
            return "2.5"
        elif "qwen2" in model_name_lower:
            return "2"
        else:
            # 默认使用Qwen2
            return "2"
    
    def _get_openai_encoding_name(self, model_name: str) -> str:
        """根据模型名称获取编码器名称，使用tiktoken官方映射

        Args:
            model_name: 模型名称

        Returns:
            编码器名称
        """
        model_name_lower = model_name.lower()

        # 首先检查完整模型名称
        if model_name_lower in self.model_to_encoding:
            return self.model_to_encoding[model_name_lower]

        # 然后检查模型前缀
        for prefix, encoding in self.model_prefix_to_encoding.items():
            if model_name_lower.startswith(prefix.lower()):
                return encoding

        # 默认使用cl100k_base（适用于大多数现代模型）
        logger.warning(f"未知模型 {model_name}，使用默认编码器 cl100k_base")
        return "cl100k_base"
    
    def count_tokens(self, text: str, tokenizer_type: str = "openai", model_name: str = "gpt-4") -> int:
        """计算token数量

        Args:
            text: 文本内容
            tokenizer_type: tokenizer类型
            model_name: 模型名称

        Returns:
            token数量
        """
        tokenizer = self.get_tokenizer(tokenizer_type, model_name)

        if tokenizer:
            try:
                if tokenizer_type.lower() == "openai":
                    # tiktoken tokenizer
                    return len(tokenizer.encode(text))
                else:
                    # tokenizers库的tokenizer (Qwen)
                    encoding = tokenizer.encode(text)
                    return len(encoding.ids)
            except Exception as e:
                logger.warning(f"token计数失败: {e}")

        # 回退到字符估算
        return self._estimate_tokens_from_chars(text)
    
    def _estimate_tokens_from_chars(self, text: str) -> int:
        """基于字符数估算token数量
        
        Args:
            text: 文本内容
            
        Returns:
            估算的token数量
        """
        chinese_chars = sum(1 for char in text if "\u4e00" <= char <= "\u9fff")
        other_chars = len(text) - chinese_chars
        # 中文字符按1:1计算，英文按1:4计算
        return chinese_chars + max(1, other_chars // 4)
    
    def is_available(self, tokenizer_type: str = "openai") -> bool:
        """检查tokenizer是否可用

        Args:
            tokenizer_type: tokenizer类型

        Returns:
            是否可用
        """
        try:
            if tokenizer_type.lower() == "openai":
                # 检查tiktoken是否可用
                try:
                    import tiktoken
                    # 检查本地缓存目录是否存在
                    return self.tiktoken_cache_dir.exists()
                except ImportError:
                    return False
            elif tokenizer_type.lower() == "qwen":
                # 检查任一版本的Qwen tokenizer是否存在
                for version in ["2", "2.5", "3"]:
                    qwen_dir = self.tokenizers_dir / f"qwen{version}"
                    if (qwen_dir / "tokenizer.json").exists():
                        return True
                return False
            return False
        except Exception:
            return False


# 全局tokenizer管理器实例
_tokenizer_manager = None

def get_tokenizer_manager() -> LocalTokenizerManager:
    """获取全局tokenizer管理器实例"""
    global _tokenizer_manager
    if _tokenizer_manager is None:
        _tokenizer_manager = LocalTokenizerManager()
    return _tokenizer_manager
