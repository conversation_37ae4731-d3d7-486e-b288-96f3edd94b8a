"""日志工具模块"""

import sys
from typing import Optional

from loguru import logger


def get_logger(name: Optional[str] = None):
    """获取日志记录器

    Args:
        name: 日志记录器名称

    Returns:
        配置好的日志记录器
    """
    # 移除默认的处理器
    logger.remove()

    # 添加控制台处理器
    logger.add(
        sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>",
        level="INFO",
        colorize=True,
    )

    # 如果指定了名称，返回带名称的logger
    if name:
        return logger.bind(name=name)

    return logger


def configure_logger(
    level: str = "INFO",
    log_file: Optional[str] = None,
    rotation: str = "10 MB",
    retention: str = "7 days",
    debug_mode: bool = False,
) -> None:
    """配置全局日志设置

    Args:
        level: 日志级别
        log_file: 日志文件路径
        rotation: 日志轮转大小
        retention: 日志保留时间
        debug_mode: 是否启用DEBUG模式
    """
    # 移除所有现有处理器
    logger.remove()

    # DEBUG模式下强制使用DEBUG级别
    if debug_mode:
        level = "DEBUG"

    # 添加控制台处理器
    console_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )

    # DEBUG模式下显示更详细的信息
    if debug_mode:
        console_format = (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )

    logger.add(
        sys.stderr,
        format=console_format,
        level=level,
        colorize=True,
    )

    # 如果指定了日志文件，添加文件处理器
    if log_file:
        file_format = "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}"
        if debug_mode:
            file_format = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}"

        logger.add(
            log_file,
            format=file_format,
            level=level,
            rotation=rotation,
            retention=retention,
            encoding="utf-8",
        )
