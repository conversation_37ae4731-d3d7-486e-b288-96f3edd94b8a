"""提取请求模型"""

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class ExtractionRequest(BaseModel):
    """提取请求模型

    Args:
        text_content: 待提取的文档文本内容
        schema: JSON Schema定义
        config_version_id: 配置版本ID(可选)
        document_id: 文档ID(可选)
        prompt_template: 自定义提示词模板(可选)
        few_shot_examples: 少样本示例(可选)
        custom_instructions: 自定义指令(可选)
    """

    text_content: str = Field(description="待提取的文档文本内容")
    schema: Dict[str, Any] = Field(description="JSON Schema定义")

    # 可选参数
    config_version_id: Optional[str] = Field(default=None, description="配置版本ID")
    document_id: Optional[str] = Field(default=None, description="文档ID")
    prompt_template: Optional[str] = Field(default=None, description="自定义提示词模板")
    few_shot_examples: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="少样本示例"
    )
    custom_instructions: Optional[str] = Field(default=None, description="自定义指令")

    class Config:
        """Pydantic配置"""

        extra = "forbid"
        validate_assignment = True
