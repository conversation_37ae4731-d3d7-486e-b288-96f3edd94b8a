"""提取结果模型"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class ExtractionStatus(str, Enum):
    """提取状态枚举"""

    SUCCESS = "success"
    FAILED = "failed"
    PARTIAL = "partial"


class ExtractionError(BaseModel):
    """提取错误模型

    Args:
        error_type: 错误类型
        error_message: 错误消息
        error_details: 错误详情
        chunk_index: 出错的文本块索引(可选)
    """

    error_type: str = Field(description="错误类型")
    error_message: str = Field(description="错误消息")
    error_details: Optional[Dict[str, Any]] = Field(
        default=None, description="错误详情"
    )
    chunk_index: Optional[int] = Field(default=None, description="出错的文本块索引")


class ExtractionResult(BaseModel):
    """提取结果模型

    Args:
        status: 提取状态
        extracted_data: 提取的数据
        errors: 错误列表
        metadata: 元数据信息
        processing_time: 处理时间(秒)
        created_at: 创建时间
    """

    status: ExtractionStatus = Field(description="提取状态")
    extracted_data: Optional[Dict[str, Any]] = Field(
        default=None, description="提取的数据"
    )
    errors: List[ExtractionError] = Field(default_factory=list, description="错误列表")

    # 元数据
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据信息")
    processing_time: Optional[float] = Field(default=None, description="处理时间(秒)")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")

    class Config:
        """Pydantic配置"""

        extra = "forbid"
        validate_assignment = True

    def add_error(
        self,
        error_type: str,
        error_message: str,
        error_details: Optional[Dict[str, Any]] = None,
        chunk_index: Optional[int] = None,
    ) -> None:
        """添加错误信息

        Args:
            error_type: 错误类型
            error_message: 错误消息
            error_details: 错误详情
            chunk_index: 出错的文本块索引
        """
        error = ExtractionError(
            error_type=error_type,
            error_message=error_message,
            error_details=error_details,
            chunk_index=chunk_index,
        )
        self.errors.append(error)

        # 更新状态 - 如果有提取数据则为PARTIAL，否则为FAILED
        if self.status == ExtractionStatus.SUCCESS:
            self.status = (
                ExtractionStatus.PARTIAL
                if self.extracted_data
                else ExtractionStatus.FAILED
            )

    def is_success(self) -> bool:
        """判断是否成功"""
        return self.status == ExtractionStatus.SUCCESS

    def has_errors(self) -> bool:
        """判断是否有错误"""
        return len(self.errors) > 0
