"""错误处理与重试模块"""

import asyncio
from typing import Any, Callable, Dict, List, Optional

from tenacity import (
    RetryError,
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

from ..config import ExtractionConfig
from ..models.extraction_result import ExtractionResult, ExtractionStatus
from ..utils.logger import get_logger

logger = get_logger(__name__)


class ErrorHandler:
    """错误处理与重试器

    负责处理提取过程中的各种错误并实现重试逻辑
    """

    def __init__(self, config: ExtractionConfig):
        """初始化错误处理器

        Args:
            config: 提取配置
        """
        self.config = config

    async def execute_with_retry(self, func: Callable, *args, **kwargs) -> Any:
        """带重试的执行函数

        Args:
            func: 要执行的函数
            *args: 位置参数
            **kwargs: 关键字参数

        Returns:
            函数执行结果

        Raises:
            Exception: 重试耗尽后的最后一个异常
        """

        @retry(
            stop=stop_after_attempt(self.config.max_retries + 1),
            wait=wait_exponential(
                multiplier=self.config.retry_delay,
                min=self.config.retry_delay,
                max=self.config.retry_delay * 10,
            ),
            retry=retry_if_exception_type(
                (
                    ConnectionError,
                    TimeoutError,
                    Exception,  # 可以根据需要调整重试的异常类型
                )
            ),
        )
        async def _execute():
            if asyncio.iscoroutinefunction(func):
                return await func(*args, **kwargs)
            else:
                return func(*args, **kwargs)

        try:
            return await _execute()
        except RetryError as e:
            logger.error(f"重试耗尽，最终失败: {str(e.last_attempt.exception())}")
            raise e.last_attempt.exception()

    def handle_llm_error(
        self,
        error: Exception,
        result: ExtractionResult,
        chunk_index: Optional[int] = None,
    ) -> None:
        """处理LLM调用错误

        Args:
            error: 异常对象
            result: 提取结果对象
            chunk_index: 文本块索引
        """
        error_type = type(error).__name__
        error_message = str(error)

        logger.error(f"LLM调用错误 (块 {chunk_index}): {error_type} - {error_message}")

        result.add_error(
            error_type=f"LLM_{error_type}",
            error_message=f"LLM调用失败: {error_message}",
            error_details={"exception_type": error_type},
            chunk_index=chunk_index,
        )

    def handle_parsing_error(
        self,
        error: Exception,
        raw_response: str,
        result: ExtractionResult,
        chunk_index: Optional[int] = None,
    ) -> None:
        """处理解析错误

        Args:
            error: 异常对象
            raw_response: 原始响应
            result: 提取结果对象
            chunk_index: 文本块索引
        """
        error_type = type(error).__name__
        error_message = str(error)

        logger.error(f"解析错误 (块 {chunk_index}): {error_type} - {error_message}")
        logger.debug(f"原始响应: {raw_response[:500]}...")

        result.add_error(
            error_type=f"PARSING_{error_type}",
            error_message=f"响应解析失败: {error_message}",
            error_details={
                "exception_type": error_type,
                "raw_response_preview": raw_response[:200],
            },
            chunk_index=chunk_index,
        )

    def handle_validation_errors(
        self,
        validation_errors: List[str],
        result: ExtractionResult,
        chunk_index: Optional[int] = None,
    ) -> None:
        """处理校验错误

        Args:
            validation_errors: 校验错误列表
            result: 提取结果对象
            chunk_index: 文本块索引
        """
        for error_msg in validation_errors:
            logger.warning(f"校验错误 (块 {chunk_index}): {error_msg}")

            result.add_error(
                error_type="VALIDATION_ERROR",
                error_message=error_msg,
                chunk_index=chunk_index,
            )

    def handle_timeout_error(
        self, result: ExtractionResult, chunk_index: Optional[int] = None
    ) -> None:
        """处理超时错误

        Args:
            result: 提取结果对象
            chunk_index: 文本块索引
        """
        error_message = f"请求超时 (超过 {self.config.llm_timeout} 秒)"

        logger.error(f"超时错误 (块 {chunk_index}): {error_message}")

        result.add_error(
            error_type="TIMEOUT_ERROR",
            error_message=error_message,
            chunk_index=chunk_index,
        )

    def handle_rate_limit_error(
        self, result: ExtractionResult, chunk_index: Optional[int] = None
    ) -> None:
        """处理速率限制错误

        Args:
            result: 提取结果对象
            chunk_index: 文本块索引
        """
        error_message = "API调用频率超限"

        logger.warning(f"速率限制 (块 {chunk_index}): {error_message}")

        result.add_error(
            error_type="RATE_LIMIT_ERROR",
            error_message=error_message,
            chunk_index=chunk_index,
        )

    def create_self_correction_prompt(
        self, original_prompt: str, failed_response: str, error_message: str
    ) -> str:
        """创建自我修正提示词

        Args:
            original_prompt: 原始提示词
            failed_response: 失败的响应
            error_message: 错误消息

        Returns:
            自我修正提示词
        """
        correction_prompt = f"""你之前的回答有问题，需要修正。

原始任务：
{original_prompt}

你之前的回答：
{failed_response}

错误信息：
{error_message}

请修正你的回答，确保：
1. 返回有效的JSON格式
2. 严格按照Schema要求
3. 不要包含任何额外的解释或文字

请重新提供正确的JSON结果："""

        return correction_prompt

    def should_attempt_correction(self, error_type: str) -> bool:
        """判断是否应该尝试自我修正

        Args:
            error_type: 错误类型

        Returns:
            是否应该尝试修正
        """
        correctable_errors = [
            "PARSING_JSONDecodeError",
            "VALIDATION_ERROR",
        ]

        return error_type in correctable_errors

    def get_error_summary(self, result: ExtractionResult) -> Dict[str, Any]:
        """获取错误摘要

        Args:
            result: 提取结果

        Returns:
            错误摘要
        """
        if not result.has_errors():
            return {"total_errors": 0, "error_types": {}}

        error_types = {}
        for error in result.errors:
            error_type = error.error_type
            if error_type not in error_types:
                error_types[error_type] = 0
            error_types[error_type] += 1

        return {
            "total_errors": len(result.errors),
            "error_types": error_types,
            "has_chunk_errors": any(
                error.chunk_index is not None for error in result.errors
            ),
            "failed_chunks": list(
                set(
                    error.chunk_index
                    for error in result.errors
                    if error.chunk_index is not None
                )
            ),
        }
