"""结果解析与校验模块"""

import json
import re
from typing import Any, Dict, List, Optional, Tuple

import jsonschema

from ..utils.logger import get_logger

logger = get_logger(__name__)


class ResultParser:
    """结果解析与校验器

    负责解析LLM返回的结果并进行Schema校验
    """

    def __init__(self):
        """初始化结果解析器"""
        pass

    def parse_and_validate(
        self, raw_response: str, schema: Dict[str, Any]
    ) -> Tuple[Optional[Dict[str, Any]], List[str]]:
        """解析并校验LLM响应

        Args:
            raw_response: LLM原始响应
            schema: JSON Schema

        Returns:
            (解析结果, 错误列表)
        """
        errors = []

        # 1. 解析JSON
        parsed_data = self._parse_json(raw_response)
        if parsed_data is None:
            errors.append("无法解析为有效的JSON格式")
            return None, errors

        # 2. Schema校验
        validation_errors = self._validate_schema(parsed_data, schema)
        if validation_errors:
            errors.extend(validation_errors)

        # 3. 数据清洗和标准化
        if not errors:
            cleaned_data = self._clean_and_normalize(parsed_data, schema)
            return cleaned_data, []

        return parsed_data, errors

    def _parse_json(self, raw_response: str) -> Optional[Dict[str, Any]]:
        """解析JSON字符串

        Args:
            raw_response: 原始响应字符串

        Returns:
            解析后的字典或None
        """
        # 清理响应文本
        cleaned_response = self._clean_response_text(raw_response)

        # 尝试直接解析
        try:
            return json.loads(cleaned_response)
        except json.JSONDecodeError:
            pass

        # 尝试提取JSON块
        json_content = self._extract_json_block(cleaned_response)
        if json_content:
            try:
                return json.loads(json_content)
            except json.JSONDecodeError:
                pass

        # 尝试修复常见的JSON错误
        fixed_json = self._fix_common_json_errors(cleaned_response)
        if fixed_json:
            try:
                return json.loads(fixed_json)
            except json.JSONDecodeError:
                pass

        logger.error(f"无法解析JSON: {raw_response[:200]}...")
        return None

    def _clean_response_text(self, text: str) -> str:
        """清理响应文本

        Args:
            text: 原始文本

        Returns:
            清理后的文本
        """
        # 移除前后空白
        text = text.strip()

        # 移除常见的前缀和后缀
        prefixes_to_remove = [
            "```json",
            "```",
            "JSON:",
            "结果:",
            "提取结果:",
            "答案:",
        ]

        suffixes_to_remove = [
            "```",
            "以上就是提取的结果",
            "这是提取的信息",
        ]

        for prefix in prefixes_to_remove:
            if text.startswith(prefix):
                text = text[len(prefix) :].strip()

        for suffix in suffixes_to_remove:
            if text.endswith(suffix):
                text = text[: -len(suffix)].strip()

        return text

    def _extract_json_block(self, text: str) -> Optional[str]:
        """从文本中提取JSON块

        Args:
            text: 文本内容

        Returns:
            提取的JSON字符串或None
        """
        # 查找JSON代码块
        json_block_pattern = r"```(?:json)?\s*(\{.*?\})\s*```"
        match = re.search(json_block_pattern, text, re.DOTALL | re.IGNORECASE)
        if match:
            return match.group(1).strip()

        # 查找花括号包围的内容
        brace_pattern = r"\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}"
        matches = re.findall(brace_pattern, text, re.DOTALL)
        if matches:
            # 返回最长的匹配
            return max(matches, key=len)

        return None

    def _fix_common_json_errors(self, text: str) -> Optional[str]:
        """修复常见的JSON错误

        Args:
            text: JSON文本

        Returns:
            修复后的JSON字符串或None
        """
        try:
            # 修复尾随逗号
            text = re.sub(r",\s*}", "}", text)
            text = re.sub(r",\s*]", "]", text)

            # 修复单引号
            text = re.sub(r"'([^']*)':", r'"\1":', text)
            text = re.sub(r":\s*'([^']*)'", r': "\1"', text)

            # 修复未引用的键
            text = re.sub(r"(\w+):", r'"\1":', text)

            return text
        except Exception:
            return None

    def _validate_schema(
        self, data: Dict[str, Any], schema: Dict[str, Any]
    ) -> List[str]:
        """校验数据是否符合Schema

        Args:
            data: 待校验的数据
            schema: JSON Schema

        Returns:
            错误列表
        """
        errors = []

        try:
            jsonschema.validate(data, schema)
        except jsonschema.ValidationError as e:
            errors.append(f"Schema校验失败: {e.message}")
        except jsonschema.SchemaError as e:
            errors.append(f"Schema定义错误: {e.message}")
        except Exception as e:
            errors.append(f"校验过程出错: {str(e)}")

        return errors

    def _clean_and_normalize(
        self, data: Dict[str, Any], schema: Dict[str, Any]
    ) -> Dict[str, Any]:
        """清洗和标准化数据

        Args:
            data: 原始数据
            schema: JSON Schema

        Returns:
            清洗后的数据
        """
        cleaned_data = {}
        properties = schema.get("properties", {})

        for key, value in data.items():
            if key in properties:
                field_schema = properties[key]
                cleaned_value = self._clean_field_value(value, field_schema)
                cleaned_data[key] = cleaned_value
            else:
                cleaned_data[key] = value

        return cleaned_data

    def _clean_field_value(self, value: Any, field_schema: Dict[str, Any]) -> Any:
        """清洗字段值

        Args:
            value: 字段值
            field_schema: 字段Schema

        Returns:
            清洗后的值
        """
        field_type = field_schema.get("type")
        field_format = field_schema.get("format")

        if value is None:
            return None

        # 字符串类型清洗
        if field_type == "string":
            if not isinstance(value, str):
                value = str(value)

            # 清理空白字符
            value = value.strip()

            # 格式特定清洗
            if field_format == "date":
                value = self._clean_date_string(value)
            elif field_format == "email":
                value = self._clean_email_string(value)
            elif field_format == "phone":
                value = self._clean_phone_string(value)

        # 数字类型清洗
        elif field_type in ["number", "integer"]:
            if isinstance(value, str):
                # 移除非数字字符
                cleaned_num = re.sub(r"[^\d.-]", "", value)
                try:
                    value = (
                        float(cleaned_num)
                        if field_type == "number"
                        else int(float(cleaned_num))
                    )
                except ValueError:
                    value = None

        return value

    def _clean_date_string(self, date_str: str) -> str:
        """清洗日期字符串

        Args:
            date_str: 日期字符串

        Returns:
            清洗后的日期字符串
        """
        # 移除常见的日期分隔符周围的空格
        date_str = re.sub(r"\s*[-/年月日]\s*", lambda m: m.group(0).strip(), date_str)
        return date_str

    def _clean_email_string(self, email_str: str) -> str:
        """清洗邮箱字符串

        Args:
            email_str: 邮箱字符串

        Returns:
            清洗后的邮箱字符串
        """
        return email_str.lower().strip()

    def _clean_phone_string(self, phone_str: str) -> str:
        """清洗电话号码字符串

        Args:
            phone_str: 电话号码字符串

        Returns:
            清洗后的电话号码字符串
        """
        # 移除常见的分隔符和空格
        phone_str = re.sub(r"[-\s()]", "", phone_str)
        return phone_str
