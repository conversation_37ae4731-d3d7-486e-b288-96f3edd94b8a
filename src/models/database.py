"""数据库模型定义"""

from datetime import datetime
from sqlalchemy import Column, String, DateTime, Text, Integer, Float, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()


class Task(Base):
    """任务表模型"""
    __tablename__ = "tasks"

    task_id = Column(String(36), primary_key=True, index=True)
    status = Column(String(20), nullable=False, default="pending", index=True)
    filename = Column(String(255), nullable=True)
    file_size = Column(Integer, nullable=True)
    file_path = Column(String(500), nullable=True)
    
    # 提取配置
    extraction_config = Column(JSON, nullable=True)
    extraction_schema = Column(JSON, nullable=True)
    prompt_template = Column(Text, nullable=True)
    few_shot_examples = Column(JSON, nullable=True)
    custom_instructions = Column(Text, nullable=True)
    
    # 处理结果
    markdown_content = Column(Text, nullable=True)
    extracted_data = Column(JSON, nullable=True)
    processing_time = Column(Float, nullable=True)
    progress = Column(Float, default=0.0)
    
    # 错误信息
    error_message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True)
    
    # 元数据
    task_metadata = Column(JSON, nullable=True)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    def to_dict(self):
        """转换为字典"""
        return {
            "task_id": self.task_id,
            "status": self.status,
            "filename": self.filename,
            "file_size": self.file_size,
            "progress": self.progress,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "error_message": self.error_message,
            "extracted_data": self.extracted_data,
            "processing_time": self.processing_time,
            "markdown_content": self.markdown_content,
            "metadata": self.metadata
        }
