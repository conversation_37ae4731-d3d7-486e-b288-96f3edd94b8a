"""数据模型定义"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class ExtractionConfigRequest(BaseModel):
    """提取配置请求模型"""
    llm_base_url: str = Field(..., description="LLM API基础URL")
    llm_api_key: str = Field(..., description="LLM API密钥")
    llm_model: str = Field(default="gpt-4", description="LLM模型名称")
    llm_temperature: float = Field(default=0.1, ge=0.0, le=2.0, description="温度参数")
    llm_max_tokens: int = Field(default=4000, gt=0, description="最大token数")
    llm_timeout: int = Field(default=60, gt=0, description="请求超时时间(秒)")
    max_retries: int = Field(default=3, ge=0, description="最大重试次数")
    retry_delay: float = Field(default=1.0, ge=0, description="重试延迟(秒)")
    max_chunk_size: int = Field(default=8000, gt=0, description="最大文本块大小")
    chunk_overlap: int = Field(default=200, ge=0, description="文本块重叠大小")
    enable_parallel: bool = Field(default=True, description="是否启用并行处理")
    max_parallel_chunks: int = Field(default=5, gt=0, description="最大并行处理块数")


class ExtractionRequest(BaseModel):
    """信息提取请求模型"""
    schema: Dict[str, Any] = Field(..., description="JSON Schema定义")
    config: ExtractionConfigRequest = Field(..., description="提取配置")
    prompt_template: Optional[str] = Field(None, description="自定义提示词模板")
    few_shot_examples: Optional[List[Dict[str, Any]]] = Field(None, description="少样本示例")
    custom_instructions: Optional[str] = Field(None, description="自定义指令")


class TaskResponse(BaseModel):
    """任务响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    filename: Optional[str] = Field(None, description="文件名")
    file_size: Optional[int] = Field(None, description="文件大小")
    progress: float = Field(default=0.0, ge=0.0, le=100.0, description="进度百分比")
    message: Optional[str] = Field(None, description="状态消息")
    error_message: Optional[str] = Field(None, description="错误消息")


class TaskResult(BaseModel):
    """任务结果模型"""
    task_id: str = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    extracted_data: Optional[Dict[str, Any]] = Field(None, description="提取的数据")
    processing_time: Optional[float] = Field(None, description="处理时间(秒)")
    markdown_content: Optional[str] = Field(None, description="转换后的markdown内容")
    error_message: Optional[str] = Field(None, description="错误消息")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")


class UploadResponse(BaseModel):
    """文件上传响应模型"""
    task_id: str = Field(..., description="任务ID")
    filename: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小")
    message: str = Field(..., description="响应消息")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
