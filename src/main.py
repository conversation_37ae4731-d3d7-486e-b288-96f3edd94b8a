"""FastAPI主应用"""

import logging
import os
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware

from src.core.database import init_database
from src.api import upload, tasks

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化数据库
    logger.info("初始化数据库...")
    await init_database()
    logger.info("数据库初始化完成")
    
    # 创建必要的目录
    os.makedirs("uploads", exist_ok=True)
    os.makedirs("frontend", exist_ok=True)
    
    yield
    
    # 关闭时的清理工作
    logger.info("应用关闭")


# 创建FastAPI应用
app = FastAPI(
    title="Memect Insight Extractor Web Interface",
    description="基于 memect_insight_extractor 库的 PDF 文档信息提取 Web 服务",
    version="1.0.0",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(upload.router)
app.include_router(tasks.router)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="frontend"), name="static")


@app.get("/")
async def root():
    """根路径，返回前端页面"""
    frontend_path = "frontend/index.html"
    if os.path.exists(frontend_path):
        return FileResponse(frontend_path)
    else:
        return {"message": "Memect Insight Extractor Web Interface", "status": "running"}


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "memect-insight-extractor-web",
        "version": "1.0.0"
    }


@app.get("/api/config/test")
async def test_config():
    """测试配置接口"""
    return {
        "message": "配置测试接口",
        "supported_models": [
            "gpt-4",
            "gpt-3.5-turbo",
            "claude-3-sonnet",
            "qwen-plus"
        ],
        "default_config": {
            "llm_base_url": "https://api.openai.com/v1",
            "llm_model": "gpt-4",
            "llm_temperature": 0.1,
            "llm_max_tokens": 4000,
            "max_chunk_size": 8000,
            "enable_parallel": True
        }
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "src.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
