# 基于Token的智能文本分割

本模块支持基于token数的智能文本分割，能够更精确地控制发送给大语言模型的文本长度，同时保持文档结构的完整性。

## 功能特性

### 1. 多Tokenizer支持

支持两种tokenizer类型：

- **OpenAI Tokenizer**: 使用tiktoken库，适用于GPT系列模型
- **Qwen Tokenizer**: 使用transformers库，适用于Qwen系列模型

### 2. Markdown格式支持

- 智能识别Markdown标题（H1-H6）
- 保护代码块完整性
- 保护表格结构
- 支持引用块和其他Markdown元素

### 3. 章节结构保护

- 优先在章节边界分割
- 只有当单个章节超过token限制时才进行语义分割
- 保持文档逻辑结构的完整性

## 配置选项

```python
from memect_insight_extractor import ExtractionConfig

config = ExtractionConfig(
    llm_api_key="your-api-key",
    
    # Token分割配置
    max_chunk_tokens=2000,        # 最大token数
    chunk_overlap_tokens=100,     # token重叠数
    tokenizer_type="openai",      # tokenizer类型: "openai" 或 "qwen"
    
    # 字符分割配置（备用）
    max_chunk_size=8000,          # 最大字符数
    chunk_overlap=400,            # 字符重叠数
)
```

## 使用示例

### 基本使用

```python
from memect_insight_extractor import DocumentExtractor, ExtractionConfig

# 配置OpenAI tokenizer
config = ExtractionConfig(
    llm_api_key="your-api-key",
    llm_model="gpt-4",
    max_chunk_tokens=2000,
    tokenizer_type="openai"
)

extractor = DocumentExtractor(config)

# 处理Markdown文档
markdown_text = """
# 第一章 项目介绍

这是项目的基本介绍...

## 1.1 背景

详细的背景信息...

```python
def example_function():
    return "Hello, World!"
```

## 1.2 目标

项目目标和期望...

# 第二章 技术实现

技术实现的详细说明...
"""

schema = {
    "type": "object",
    "properties": {
        "chapters": {
            "type": "array",
            "items": {"type": "string"},
            "description": "文档章节列表"
        },
        "key_points": {
            "type": "array", 
            "items": {"type": "string"},
            "description": "关键要点"
        }
    }
}

result = await extractor.extract(
    text_content=markdown_text,
    schema=schema
)
```

### 使用Qwen Tokenizer

```python
# 配置Qwen tokenizer
config = ExtractionConfig(
    llm_api_key="your-api-key",
    llm_model="qwen-turbo",
    max_chunk_tokens=1500,
    tokenizer_type="qwen"
)

extractor = DocumentExtractor(config)

# 其他使用方式相同
result = await extractor.extract(
    text_content=text,
    schema=schema
)
```

## 分割策略

### 1. 优先级顺序

1. **章节边界**: 优先在Markdown标题处分割
2. **段落边界**: 在段落分隔符（\n\n）处分割
3. **语义边界**: 在句子结束符处分割
4. **强制分割**: 达到token限制时强制分割

### 2. 特殊处理

- **代码块**: 保持```包围的代码块完整性
- **表格**: 保持Markdown表格的完整性
- **引用块**: 保持>开头的引用块完整性

### 3. Token计算

- 使用对应模型的官方tokenizer进行精确计算
- 支持中英文混合文本的准确token计数
- 自动回退到字符估算（当tokenizer不可用时）

## 性能优化

### 1. 完全离线运行

- 所有tokenizer文件在库安装时就已包含
- 无需联网下载，支持完全离线环境
- 使用 `tokenizers` 库提供统一的token计算接口

### 2. 智能回退机制

- 优先使用 `tiktoken` 库（OpenAI官方）
- 回退到 `tokenizers` 库（HuggingFace）
- 最终回退到字符估算

### 3. 内存优化

- 按需加载tokenizer
- 支持延迟初始化和缓存
- 错误时自动回退到轻量级估算

## 错误处理

### 1. Tokenizer不可用

```python
# 当tiktoken或transformers不可用时，自动回退到字符估算
config = ExtractionConfig(
    llm_api_key="your-api-key",
    tokenizer_type="openai"  # 即使tiktoken不可用也能正常工作
)
```

### 2. 模型下载失败

```python
# Qwen tokenizer模型下载失败时会回退到字符估算
config = ExtractionConfig(
    llm_api_key="your-api-key",
    tokenizer_type="qwen"  # 网络问题时自动回退
)
```

## 最佳实践

### 1. Token数设置

- **GPT-4**: 建议max_chunk_tokens=2000-3000
- **GPT-3.5**: 建议max_chunk_tokens=1500-2000
- **Qwen**: 建议max_chunk_tokens=1500-2500

### 2. 重叠设置

- 一般设置为max_chunk_tokens的5-10%
- 对于技术文档，可以适当增加重叠
- 对于小说等连续文本，可以减少重叠

### 3. Tokenizer选择

- 使用OpenAI模型时选择"openai"
- 使用Qwen模型时选择"qwen"
- 不确定时可以选择"openai"（通用性更好）

## 安装和部署

### 1. 开发环境

```bash
# 克隆项目
git clone <repository-url>
cd memect_insight_extractor

# 安装依赖并下载tokenizer文件
make setup

# 或者手动下载
python scripts/download_tokenizers.py
```

### 2. 生产环境

```bash
# 构建包（会自动包含tokenizer文件）
make build

# 安装到目标环境
pip install dist/memect_insight_extractor-*.whl
```

### 3. 离线环境

- 所有tokenizer文件都包含在安装包中
- 无需额外配置或下载
- 支持完全离线运行

## 注意事项

1. **完全离线**: 所有tokenizer文件在安装时就已包含，无需联网
2. **自动回退**: 如果某个tokenizer不可用，会自动回退到字符估算
3. **精确度**: OpenAI tokenizer对英文更精确，Qwen tokenizer对中文更精确
4. **兼容性**: 所有配置都向后兼容，可以无缝升级
