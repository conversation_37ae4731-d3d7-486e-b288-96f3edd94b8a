# 常见问题

## 安装和配置

### Q: 如何安装模块？

A: 推荐使用 uv 进行安装：

```bash
uv pip install memect-insight-extractor
```

或者从源码安装：

```bash
git clone <repository-url>
cd memect_insight_extractor
make setup
```

### Q: 支持哪些 Python 版本？

A: 模块要求 Python 3.12 或更高版本。

### Q: 如何配置不同的 LLM 提供商？

A: 模块支持任何兼容 OpenAI API 格式的服务：

```python
# OpenAI
config = ExtractionConfig(
    llm_base_url="https://api.openai.com/v1",
    llm_api_key="sk-...",
    llm_model="gpt-4"
)

# Azure OpenAI
config = ExtractionConfig(
    llm_base_url="https://your-resource.openai.azure.com/",
    llm_api_key="your-azure-key",
    llm_model="gpt-4",
    extra_headers={"api-version": "2023-12-01-preview"}
)
```

## 使用问题

### Q: 如何处理超长文档？

A: 模块会自动检测文档长度并进行智能分段：

```python
config = ExtractionConfig(
    llm_api_key="your-api-key",
    max_chunk_tokens=2000,  # 调整块大小
    chunk_overlap_tokens=100,  # 设置重叠
    enable_parallel=True  # 启用并行处理
)
```

### Q: 提取结果不准确怎么办？

A: 可以尝试以下方法改善：

1. **优化提示词**：
```python
custom_prompt = """
你是专业的信息提取专家。
请仔细阅读文档，准确提取信息。
注意：金额请转换为数字，日期保持原格式。
"""

result = await extractor.extract(
    text_content=text,
    schema=schema,
    prompt_template=custom_prompt
)
```

2. **提供示例**：
```python
examples = [
    {
        "input": "合同金额：5万元",
        "output": {"amount": 50000}
    }
]

result = await extractor.extract(
    text_content=text,
    schema=schema,
    few_shot_examples=examples
)
```

3. **调整模型参数**：
```python
config = ExtractionConfig(
    llm_api_key="your-api-key",
    llm_model="gpt-4",  # 使用更强的模型
    llm_temperature=0.1  # 降低随机性
)
```

### Q: 如何处理提取错误？

A: 检查 `ExtractionResult` 的状态和错误信息：

```python
result = await extractor.extract(text, schema)

if result.is_success():
    print("提取成功")
elif result.status.value == "partial":
    print("部分成功，存在问题:")
    for error in result.errors:
        print(f"- {error.error_message}")
else:
    print("提取失败:")
    for error in result.errors:
        print(f"- {error.error_type}: {error.error_message}")
```

### Q: 如何提高处理速度？

A: 可以通过以下方式优化性能：

1. **启用并行处理**：
```python
config = ExtractionConfig(
    llm_api_key="your-api-key",
    enable_parallel=True,
    max_parallel_chunks=5
)
```

2. **调整块大小**：
```python
config = ExtractionConfig(
    llm_api_key="your-api-key",
    max_chunk_tokens=3000  # 增大块大小减少分段
)
```

3. **使用更快的模型**：
```python
config = ExtractionConfig(
    llm_api_key="your-api-key",
    llm_model="gpt-3.5-turbo"  # 更快但可能精度略低
)
```

## Schema 设计

### Q: 如何设计好的 JSON Schema？

A: 遵循以下最佳实践：

1. **明确的字段描述**：
```python
schema = {
    "type": "object",
    "properties": {
        "company_name": {
            "type": "string",
            "description": "公司全称，不包含有限公司等后缀"
        },
        "amount": {
            "type": "number", 
            "description": "金额数值，单位为元，不包含货币符号"
        }
    }
}
```

2. **合理的数据类型**：
```python
# 好的设计
"date": {"type": "string", "description": "日期，格式如2024-01-15"}
"amount": {"type": "number", "description": "数值金额"}

# 避免的设计
"date": {"type": "object"}  # 过于复杂
"amount": {"type": "string"}  # 类型不当
```

3. **设置必需字段**：
```python
schema = {
    "type": "object",
    "properties": {...},
    "required": ["company_name", "amount"]  # 明确必需字段
}
```

### Q: 如何处理复杂的嵌套结构？

A: 可以设计嵌套的 Schema：

```python
schema = {
    "type": "object",
    "properties": {
        "contract_info": {
            "type": "object",
            "properties": {
                "number": {"type": "string"},
                "date": {"type": "string"}
            }
        },
        "parties": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "role": {"type": "string"}
                }
            }
        }
    }
}
```

## 错误排查

### Q: 遇到 "JSON 解析错误" 怎么办？

A: 这通常是 LLM 返回的格式不正确：

1. 检查提示词是否明确要求 JSON 格式
2. 尝试提供示例输出格式
3. 使用更强的模型（如 GPT-4）
4. 启用自我修正功能（模块默认启用）

### Q: 遇到 "Schema 校验失败" 怎么办？

A: 检查以下几点：

1. Schema 定义是否正确
2. 字段类型是否合适
3. 必需字段是否都有值
4. 数据格式是否符合预期

### Q: API 调用频率限制怎么办？

A: 调整重试配置：

```python
config = ExtractionConfig(
    llm_api_key="your-api-key",
    max_retries=5,
    retry_delay=2.0  # 增加重试延迟
)
```

### Q: 如何调试提取过程？

A: 启用详细日志：

```python
from memect_insight_extractor.utils.logger import configure_logger

configure_logger(level="DEBUG", log_file="extraction.log")
```

## 性能优化

### Q: 如何减少 API 调用成本？

A: 可以采用以下策略：

1. **优化文本分段**：
```python
config = ExtractionConfig(
    max_chunk_tokens=3000,  # 增大块大小
    chunk_overlap_tokens=50  # 减少重叠
)
```

2. **使用更便宜的模型**：
```python
config = ExtractionConfig(
    llm_model="gpt-3.5-turbo"  # 成本更低
)
```

3. **减少重试次数**：
```python
config = ExtractionConfig(
    max_retries=1  # 减少重试
)
```

### Q: 内存使用过多怎么办？

A: 对于大文档，可以：

1. 减小块大小
2. 禁用并行处理
3. 分批处理文档

```python
config = ExtractionConfig(
    max_chunk_tokens=1500,
    enable_parallel=False
)
```
