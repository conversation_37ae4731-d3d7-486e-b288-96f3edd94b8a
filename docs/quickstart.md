# 快速开始

本指南将帮助您快速上手 Memect Insight Extractor。

## 安装

### 使用 uv 安装

```bash
uv pip install memect-insight-extractor
```

### 从源码安装

```bash
git clone <repository-url>
cd memect_insight_extractor
make setup
```

## 基本使用

### 1. 配置提取器

```python
from memect_insight_extractor import DocumentExtractor, ExtractionConfig

config = ExtractionConfig(
    llm_base_url="https://api.openai.com/v1",
    llm_api_key="your-api-key",
    llm_model="gpt-4",
    llm_temperature=0.1,
    max_retries=3
)

extractor = DocumentExtractor(config)
```

### 2. 定义提取Schema

```python
schema = {
    "type": "object",
    "properties": {
        "company_name": {
            "type": "string",
            "description": "公司名称"
        },
        "contract_amount": {
            "type": "number",
            "description": "合同金额"
        },
        "signing_date": {
            "type": "string",
            "format": "date",
            "description": "签署日期"
        },
        "parties": {
            "type": "array",
            "items": {"type": "string"},
            "description": "合同各方"
        }
    },
    "required": ["company_name", "contract_amount"]
}
```

### 3. 执行提取

```python
import asyncio

async def extract_document():
    text_content = """
    合同编号：2024001
    甲方：北京科技有限公司
    乙方：上海贸易有限公司
    合同金额：100,000元
    签署日期：2024年1月15日
    
    本合同由甲乙双方友好协商签署...
    """
    
    result = await extractor.extract(
        text_content=text_content,
        schema=schema
    )
    
    if result.is_success():
        print("提取成功:")
        print(f"公司名称: {result.extracted_data['company_name']}")
        print(f"合同金额: {result.extracted_data['contract_amount']}")
        print(f"签署日期: {result.extracted_data.get('signing_date', '未找到')}")
    else:
        print("提取失败:")
        for error in result.errors:
            print(f"- {error.error_message}")

# 运行提取
asyncio.run(extract_document())
```

## 高级功能

### 自定义提示词模板

```python
custom_prompt = """
你是一个专业的合同信息提取专家。
请仔细阅读以下合同内容，提取关键信息。
注意：金额请转换为数字格式，日期请保持原格式。
"""

result = await extractor.extract(
    text_content=text_content,
    schema=schema,
    prompt_template=custom_prompt
)
```

### 添加少样本示例

```python
few_shot_examples = [
    {
        "input": "甲方：ABC公司，金额：50万元",
        "output": {
            "company_name": "ABC公司",
            "contract_amount": 500000
        }
    }
]

result = await extractor.extract(
    text_content=text_content,
    schema=schema,
    few_shot_examples=few_shot_examples
)
```

### 处理长文档

对于超长文档，系统会自动进行基于token的智能分段处理：

```python
# 配置分段参数
config = ExtractionConfig(
    llm_api_key="your-api-key",
    max_chunk_tokens=2000,    # 每段最大token数
    chunk_overlap_tokens=100, # 段落重叠token数
    tokenizer_type="openai",  # 使用OpenAI tokenizer
    enable_parallel=True,     # 启用并行处理
    max_parallel_chunks=3     # 最大并行处理数
)

extractor = DocumentExtractor(config)

# 处理长文档
long_document = "..." * 10000  # 很长的文档
result = await extractor.extract(long_document, schema)

# 检查分段信息
print(f"总段数: {result.metadata.get('total_chunks', 1)}")
print(f"成功段数: {result.metadata.get('successful_chunks', 0)}")
```

## 错误处理

### 检查提取结果

```python
result = await extractor.extract(text_content, schema)

if result.is_success():
    print("提取完全成功")
elif result.status == ExtractionStatus.PARTIAL:
    print("部分成功，有一些错误:")
    for error in result.errors:
        print(f"- {error.error_message}")
else:
    print("提取失败:")
    for error in result.errors:
        print(f"- {error.error_type}: {error.error_message}")
```

### 获取详细错误信息

```python
if result.has_errors():
    for error in result.errors:
        print(f"错误类型: {error.error_type}")
        print(f"错误消息: {error.error_message}")
        if error.chunk_index is not None:
            print(f"出错段落: {error.chunk_index}")
        if error.error_details:
            print(f"详细信息: {error.error_details}")
```

## 配置选项

### LLM配置

```python
config = ExtractionConfig(
    # LLM基本配置
    llm_base_url="https://api.openai.com/v1",
    llm_api_key="your-api-key",
    llm_model="gpt-4",
    llm_temperature=0.1,
    llm_max_tokens=4000,
    llm_timeout=60,
    
    # 重试配置
    max_retries=3,
    retry_delay=1.0,
    
    # 分段配置
    max_chunk_tokens=2000,
    chunk_overlap_tokens=100,
    tokenizer_type="openai",
    
    # 并行配置
    enable_parallel=True,
    max_parallel_chunks=5
)
```

### 使用不同的LLM提供商

```python
# OpenAI
config = ExtractionConfig(
    llm_base_url="https://api.openai.com/v1",
    llm_api_key="sk-...",
    llm_model="gpt-4"
)

# Azure OpenAI
config = ExtractionConfig(
    llm_base_url="https://your-resource.openai.azure.com/",
    llm_api_key="your-azure-key",
    llm_model="gpt-4",
    extra_headers={"api-version": "2023-12-01-preview"}
)

# 其他兼容OpenAI API的服务
config = ExtractionConfig(
    llm_base_url="https://api.anthropic.com/v1",
    llm_api_key="your-anthropic-key",
    llm_model="claude-3-sonnet"
)
```

## 下一步

- 查看 [API参考](api/config.md) 了解所有配置选项
- 浏览 [示例](examples.md) 查看更多使用场景
- 阅读 [常见问题](faq.md) 解决常见问题
