# Memect Insight Extractor Web Interface

基于 memect_insight_extractor 库的 Web 界面，提供 PDF 文档信息提取服务。

## 功能概述

### 1. 配置管理
- 支持 OpenAI 兼容 API 的配置
- 配置项包括：API URL、密钥、模型名称、温度参数等
- 配置保存在浏览器 localStorage 中，方便重复使用

### 2. 文档上传与提取
- 支持 PDF 文件上传（最大 50MB）
- 使用 marker 库将 PDF 转换为 markdown 格式
- 基于 JSON Schema 定义提取结构
- 支持自定义提示词和处理指令

### 3. 异步任务处理
- 后台异步处理文档提取任务
- 实时任务状态监控和进度显示
- 支持任务结果查询和下载

### 4. 历史记录管理
- 任务历史记录查看
- 任务状态和结果管理
- 支持结果重新下载

## 技术架构

### 后端技术栈
- **FastAPI**: Web 框架
- **SQLAlchemy**: ORM 和数据库操作
- **SQLite**: 数据存储
- **marker**: PDF 转 markdown
- **memect_insight_extractor**: 核心提取库

### 前端技术栈
- **HTML5 + CSS3**: 页面结构和样式
- **Bootstrap 5**: UI 组件库
- **Vanilla JavaScript**: 交互逻辑
- **localStorage**: 本地配置存储

## API 接口

### 上传接口
- `POST /api/upload/pdf` - 上传 PDF 文件并启动提取任务
- `GET /api/upload/limits` - 获取上传限制信息

### 任务管理接口
- `GET /api/tasks/{task_id}/status` - 获取任务状态
- `GET /api/tasks/{task_id}/result` - 获取任务结果
- `GET /api/tasks/` - 获取最近任务列表
- `GET /api/tasks/{task_id}/markdown` - 获取转换后的 markdown 内容

### 配置接口
- `GET /api/config/test` - 测试配置接口

## 使用流程

1. **配置 API**: 在配置管理界面设置 LLM API 相关参数
2. **上传文档**: 选择 PDF 文件并定义提取 Schema
3. **监控进度**: 系统自动轮询任务状态，显示处理进度
4. **查看结果**: 任务完成后查看和下载提取结果
5. **历史管理**: 在历史记录中查看所有任务

## 配置说明

### LLM 配置项
- **API 基础 URL**: OpenAI 兼容的 API 端点
- **API 密钥**: 访问 LLM 服务的密钥
- **模型名称**: 使用的大语言模型
- **温度参数**: 控制输出随机性 (0.0-2.0)
- **最大 Token 数**: 单次请求的 token 限制

### 处理配置项
- **最大文本块大小**: 文档分段的最大字符数
- **最大并行块数**: 并行处理的最大块数
- **启用并行处理**: 是否启用并行处理模式

## 部署说明

### 环境要求
- Python 3.12+
- 虚拟环境支持
- SQLite 数据库

### 安装步骤
```bash
# 1. 克隆项目
git clone <repository-url>
cd memect_insight_extractor_preview

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate

# 3. 安装依赖
pip install -r requirements.txt

# 4. 启动服务
uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
```

### 访问服务
- Web 界面: http://localhost:8000
- API 文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

## 注意事项

1. **文件大小限制**: PDF 文件最大支持 50MB
2. **API 密钥安全**: 密钥存储在浏览器本地，请注意安全
3. **网络要求**: 需要访问配置的 LLM API 服务
4. **浏览器兼容**: 建议使用现代浏览器（Chrome、Firefox、Safari）

## 故障排除

### 常见问题
1. **上传失败**: 检查文件格式和大小限制
2. **提取失败**: 验证 API 配置和网络连接
3. **任务卡住**: 检查后端日志和 LLM 服务状态
4. **配置丢失**: 检查浏览器 localStorage 设置
