# 数据模型 API

## ExtractionRequest

提取请求模型，包含执行信息提取所需的所有参数。

### 属性

- **text_content** (`str`): 待提取的文档文本内容
- **schema** (`Dict[str, Any]`): JSON Schema 定义
- **config_version_id** (`Optional[str]`): 配置版本 ID
- **document_id** (`Optional[str]`): 文档 ID
- **extraction_purpose_id** (`Optional[str]`): 提取目的唯一标识
- **prompt_template** (`Optional[str]`): 自定义提示词模板（支持变量替换）
- **few_shot_examples** (`Optional[List[Dict[str, Any]]]`): 少样本示例
- **custom_instructions** (`Optional[str]`): 自定义指令

### 示例

```python
from memect_insight_extractor import ExtractionRequest

request = ExtractionRequest(
    text_content="合同内容...",
    schema={
        "type": "object",
        "properties": {
            "company_name": {"type": "string"}
        }
    },
    document_id="doc_001",
    extraction_purpose_id="contract_analysis_v1",
    prompt_template="你是专业的合同分析专家。请分析文档 {document_id}，提取 {field_names} 信息。",
    custom_instructions="请注意格式标准化"
)
```

### 提示词配置

#### 提取目的标识

`extraction_purpose_id` 用于标识提取任务的目的，便于追踪和管理不同的提取场景：

```python
request = ExtractionRequest(
    text_content="文档内容",
    schema=schema,
    extraction_purpose_id="invoice_processing_v2.1"
)
```

#### 提示词模板

支持自定义提示词模板，并支持变量替换：

```python
request = ExtractionRequest(
    text_content="合同内容",
    schema=schema,
    prompt_template="你是专业的 {extraction_purpose_id} 专家。请分析文档 {document_id}，提取 {field_names} 信息，共 {field_count} 个字段。"
)
```

#### 变量替换

在提示词中可以使用以下变量（支持 `{variable}` 和 `${variable}` 两种格式）：

- `{text_content}`: 文档内容
- `{document_id}`: 文档ID
- `{config_version_id}`: 配置版本ID
- `{extraction_purpose_id}`: 提取目的ID
- `{schema_json}`: Schema的JSON字符串
- `{field_names}`: 字段名称列表（逗号分隔）
- `{field_count}`: 字段数量

## ExtractionResult

提取结果模型，包含提取的数据和相关元信息。

### 属性

- **status** (`ExtractionStatus`): 提取状态
- **extracted_data** (`Optional[Dict[str, Any]]`): 提取的数据
- **errors** (`List[ExtractionError]`): 错误列表
- **metadata** (`Dict[str, Any]`): 元数据信息
- **processing_time** (`Optional[float]`): 处理时间（秒）
- **created_at** (`datetime`): 创建时间

### 方法

#### is_success()

判断提取是否成功。

```python
if result.is_success():
    print("提取成功")
```

#### has_errors()

判断是否有错误。

```python
if result.has_errors():
    for error in result.errors:
        print(f"错误: {error.error_message}")
```

#### add_error()

添加错误信息。

```python
result.add_error(
    error_type="VALIDATION_ERROR",
    error_message="字段验证失败",
    chunk_index=0
)
```

### 示例

```python
# 检查结果
if result.is_success():
    data = result.extracted_data
    print(f"公司名称: {data['company_name']}")
    print(f"处理时间: {result.processing_time:.2f}秒")
elif result.status == ExtractionStatus.PARTIAL:
    print("部分成功，存在一些问题:")
    print(f"已提取数据: {result.extracted_data}")
    for error in result.errors:
        print(f"错误: {error.error_message}")
else:
    print("提取失败:")
    for error in result.errors:
        print(f"错误: {error.error_message}")
```

## ExtractionStatus

提取状态枚举。

### 值

- **SUCCESS**: 提取完全成功
- **PARTIAL**: 部分成功，有一些错误但仍有数据
- **FAILED**: 提取失败

## ExtractionError

提取错误模型。

### 属性

- **error_type** (`str`): 错误类型
- **error_message** (`str`): 错误消息
- **error_details** (`Optional[Dict[str, Any]]`): 错误详情
- **chunk_index** (`Optional[int]`): 出错的文本块索引

### 常见错误类型

- **LLM_ConnectionError**: LLM 连接错误
- **LLM_TimeoutError**: LLM 超时错误
- **PARSING_JSONDecodeError**: JSON 解析错误
- **VALIDATION_ERROR**: Schema 校验错误
- **RATE_LIMIT_ERROR**: API 频率限制错误

## SchemaField

Schema 字段模型。

### 属性

- **name** (`str`): 字段名称
- **type** (`str`): 字段类型
- **description** (`Optional[str]`): 字段描述
- **required** (`bool`): 是否必需
- **is_primary_key** (`bool`): 是否为主键（用于数据合并）
- **default** (`Optional[Any]`): 默认值
- **enum** (`Optional[List[Any]]`): 枚举值
- **format** (`Optional[str]`): 格式约束
- **pattern** (`Optional[str]`): 正则表达式模式
- **minimum** (`Optional[Union[int, float]]`): 最小值
- **maximum** (`Optional[Union[int, float]]`): 最大值

## ExtractionSchema

提取 Schema 模型。

### 属性

- **schema_id** (`Optional[str]`): Schema ID
- **name** (`str`): Schema 名称
- **description** (`Optional[str]`): Schema 描述
- **version** (`str`): Schema 版本
- **fields** (`List[SchemaField]`): 字段列表
- **json_schema** (`Dict[str, Any]`): 完整的 JSON Schema

### 方法

#### to_json_schema()

转换为 JSON Schema 格式。

#### get_required_fields()

获取必需字段列表。

#### get_primary_key_fields()

获取主键字段列表。

#### get_field_by_name()

根据名称获取字段。

### 主键功能示例

```python
from memect_insight_extractor.models.schema_models import SchemaField, ExtractionSchema

# 定义带主键的字段
fields = [
    SchemaField(name="id", type="string", is_primary_key=True),
    SchemaField(name="name", type="string", required=True),
    SchemaField(name="amount", type="number"),
]

schema = ExtractionSchema(
    name="contract_schema",
    fields=fields,
    json_schema={"type": "object"}
)

# 获取主键字段
primary_keys = schema.get_primary_key_fields()  # ["id"]
```
