# 配置 API

## ExtractionConfig

提取配置类，用于配置大模型提取器的各种参数。

### 参数

#### LLM 配置

- **llm_base_url** (`str`, 默认: `"https://api.openai.com/v1"`)
  - LLM API 基础 URL
  - 支持任何兼容 OpenAI API 格式的服务

- **llm_api_key** (`str`, 必需)
  - LLM API 密钥

- **llm_model** (`str`, 默认: `"gpt-4"`)
  - LLM 模型名称

- **llm_temperature** (`float`, 默认: `0.1`, 范围: `0.0-2.0`)
  - LLM 温度参数，控制输出的随机性

- **llm_max_tokens** (`int`, 默认: `4000`)
  - LLM 最大 token 数

- **llm_timeout** (`int`, 默认: `60`)
  - LLM 请求超时时间（秒）

#### 重试配置

- **max_retries** (`int`, 默认: `3`)
  - 最大重试次数

- **retry_delay** (`float`, 默认: `1.0`)
  - 重试延迟时间（秒）

#### 文本分段配置

- **max_chunk_tokens** (`int`, 默认: `2000`)
  - 最大文本块token数

- **chunk_overlap_tokens** (`int`, 默认: `100`)
  - 文本块重叠token数

- **tokenizer_type** (`str`, 默认: `"openai"`)
  - tokenizer类型，支持 "openai" 或 "qwen"

#### 并行处理配置

- **enable_parallel** (`bool`, 默认: `True`)
  - 是否启用并行处理

- **max_parallel_chunks** (`int`, 默认: `5`)
  - 最大并行处理块数

#### 调试和日志配置

- **debug_mode** (`bool`, 默认: `False`)
  - 是否启用DEBUG模式，启用后会输出详细的调试信息

- **log_level** (`str`, 默认: `"INFO"`)
  - 日志级别，支持 "DEBUG", "INFO", "WARNING", "ERROR"

- **log_file** (`Optional[str]`, 默认: `None`)
  - 日志文件路径，如果指定则同时输出到文件

#### 其他配置

- **extra_headers** (`Dict[str, str]`, 默认: `{}`)
  - 额外的 HTTP 头

### 示例

```python
from memect_insight_extractor import ExtractionConfig

# 基本配置
config = ExtractionConfig(
    llm_api_key="your-api-key"
)

# DEBUG模式配置
debug_config = ExtractionConfig(
    llm_api_key="your-api-key",
    debug_mode=True,
    log_level="DEBUG",
    log_file="extraction_debug.log"
)
```

### DEBUG模式说明

启用DEBUG模式可以获得详细的处理信息：

```python
config = ExtractionConfig(
    llm_api_key="your-api-key",
    debug_mode=True,           # 启用调试模式
    log_level="DEBUG",         # 设置日志级别
    log_file="debug.log"       # 输出到文件
)

# 调试信息包括：
# - 完整的提示词内容
# - LLM的原始输入输出
# - Token使用统计
# - 处理过程详情
# - 错误诊断信息

# 完整配置
config = ExtractionConfig(
    llm_base_url="https://api.openai.com/v1",
    llm_api_key="your-api-key",
    llm_model="gpt-4",
    llm_temperature=0.1,
    llm_max_tokens=4000,
    llm_timeout=60,
    max_retries=3,
    retry_delay=1.0,
    max_chunk_tokens=2000,
    chunk_overlap_tokens=100,
    tokenizer_type="openai",
    enable_parallel=True,
    max_parallel_chunks=5,
    extra_headers={"Custom-Header": "value"}
)

# Azure OpenAI 配置
azure_config = ExtractionConfig(
    llm_base_url="https://your-resource.openai.azure.com/",
    llm_api_key="your-azure-key",
    llm_model="gpt-4",
    extra_headers={"api-version": "2023-12-01-preview"}
)
```

### 配置验证

配置类使用 Pydantic 进行验证，会自动检查：

- 参数类型是否正确
- 数值是否在有效范围内
- 必需参数是否提供

```python
# 这会抛出 ValidationError
config = ExtractionConfig(
    llm_api_key="test-key",
    llm_temperature=3.0  # 超出范围 0.0-2.0
)
```
