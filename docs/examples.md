# 使用示例

本页面提供了各种使用场景的详细示例。

## 基本使用

### 简单合同信息提取

```python
import asyncio
from memect_insight_extractor import DocumentExtractor, ExtractionConfig

async def extract_contract():
    # 配置
    config = ExtractionConfig(
        llm_api_key="your-api-key",
        llm_model="gpt-4"
    )
    
    extractor = DocumentExtractor(config)
    
    # 文档内容
    text = """
    合同编号：2024001
    甲方：北京科技有限公司
    乙方：上海贸易有限公司
    合同金额：100,000元
    签署日期：2024年1月15日
    """
    
    # Schema定义
    schema = {
        "type": "object",
        "properties": {
            "contract_number": {"type": "string", "description": "合同编号"},
            "party_a": {"type": "string", "description": "甲方"},
            "party_b": {"type": "string", "description": "乙方"},
            "amount": {"type": "number", "description": "合同金额"},
            "date": {"type": "string", "description": "签署日期"}
        },
        "required": ["contract_number", "party_a", "party_b"]
    }
    
    # 执行提取
    result = await extractor.extract(text, schema)
    
    if result.is_success():
        print("提取成功:")
        for key, value in result.extracted_data.items():
            print(f"  {key}: {value}")
    else:
        print("提取失败:")
        for error in result.errors:
            print(f"  {error.error_message}")

# 运行
asyncio.run(extract_contract())
```

## 高级功能示例

### 使用自定义提示词

```python
async def extract_with_custom_prompt():
    config = ExtractionConfig(llm_api_key="your-api-key")
    extractor = DocumentExtractor(config)
    
    custom_prompt = """
    你是一个专业的财务文档分析专家。
    请仔细分析以下文档，提取关键财务信息。
    
    注意事项：
    1. 金额请转换为数字格式（去除货币符号和逗号）
    2. 日期请保持原始格式
    3. 如果信息不明确，请标注为null
    
    请严格按照提供的JSON Schema格式返回结果。
    """
    
    text = "营业收入：¥1,250,000元，净利润：￥350,000元，报告期：2024年第一季度"
    
    schema = {
        "type": "object",
        "properties": {
            "revenue": {"type": "number", "description": "营业收入"},
            "profit": {"type": "number", "description": "净利润"},
            "period": {"type": "string", "description": "报告期"}
        }
    }
    
    result = await extractor.extract(
        text_content=text,
        schema=schema,
        prompt_template=custom_prompt
    )
    
    return result
```

### 少样本学习

```python
async def extract_with_examples():
    config = ExtractionConfig(llm_api_key="your-api-key")
    extractor = DocumentExtractor(config)
    
    # 提供示例
    examples = [
        {
            "input": "发票号：INV001，客户：ABC公司，金额：5000元",
            "output": {
                "invoice_number": "INV001",
                "customer": "ABC公司",
                "amount": 5000
            }
        },
        {
            "input": "票据编号：INV002，购买方：XYZ企业，总计：¥8,500",
            "output": {
                "invoice_number": "INV002",
                "customer": "XYZ企业",
                "amount": 8500
            }
        }
    ]
    
    text = "发票编号：INV003，客户名称：DEF集团，应付金额：12,000元"
    
    schema = {
        "type": "object",
        "properties": {
            "invoice_number": {"type": "string", "description": "发票号码"},
            "customer": {"type": "string", "description": "客户名称"},
            "amount": {"type": "number", "description": "金额"}
        }
    }
    
    result = await extractor.extract(
        text_content=text,
        schema=schema,
        few_shot_examples=examples
    )
    
    return result
```

## 复杂文档处理

### 长文档自动分段

```python
async def extract_long_document():
    config = ExtractionConfig(
        llm_api_key="your-api-key",
        max_chunk_tokens=1500,  # 每段最大1500 tokens
        chunk_overlap_tokens=75,  # 段落重叠75 tokens
        enable_parallel=True, # 启用并行处理
        max_parallel_chunks=3 # 最多3个并行
    )
    
    extractor = DocumentExtractor(config)
    
    # 读取长文档
    with open("long_contract.txt", "r", encoding="utf-8") as f:
        long_text = f.read()
    
    schema = {
        "type": "object",
        "properties": {
            "parties": {
                "type": "array",
                "items": {"type": "string"},
                "description": "合同各方"
            },
            "key_terms": {
                "type": "array", 
                "items": {"type": "string"},
                "description": "关键条款"
            },
            "amounts": {
                "type": "array",
                "items": {"type": "number"},
                "description": "涉及金额"
            }
        }
    }
    
    result = await extractor.extract(long_text, schema)
    
    # 检查分段信息
    print(f"文档长度: {len(long_text)} 字符")
    print(f"分段数量: {result.metadata.get('total_chunks', 1)}")
    print(f"处理时间: {result.processing_time:.2f} 秒")
    
    return result
```

### 结构化数据提取

```python
async def extract_structured_data():
    config = ExtractionConfig(llm_api_key="your-api-key")
    extractor = DocumentExtractor(config)
    
    text = """
    员工信息表
    
    姓名：张三
    工号：E001
    部门：技术部
    职位：高级工程师
    入职日期：2020-03-15
    联系电话：13812345678
    邮箱：<EMAIL>
    
    姓名：李四
    工号：E002
    部门：市场部
    职位：市场经理
    入职日期：2019-07-20
    联系电话：13987654321
    邮箱：<EMAIL>
    """
    
    schema = {
        "type": "object",
        "properties": {
            "employees": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "name": {"type": "string", "description": "姓名"},
                        "employee_id": {"type": "string", "description": "工号"},
                        "department": {"type": "string", "description": "部门"},
                        "position": {"type": "string", "description": "职位"},
                        "hire_date": {"type": "string", "description": "入职日期"},
                        "phone": {"type": "string", "description": "联系电话"},
                        "email": {"type": "string", "description": "邮箱"}
                    }
                },
                "description": "员工列表"
            }
        }
    }
    
    result = await extractor.extract(text, schema)
    
    if result.is_success():
        employees = result.extracted_data["employees"]
        print(f"提取到 {len(employees)} 名员工信息:")
        for emp in employees:
            print(f"  {emp['name']} - {emp['department']} - {emp['position']}")
    
    return result
```

## 错误处理示例

### 完整的错误处理

```python
async def robust_extraction():
    config = ExtractionConfig(
        llm_api_key="your-api-key",
        max_retries=3,
        retry_delay=2.0
    )
    
    extractor = DocumentExtractor(config)
    
    text = "模糊的文档内容..."
    schema = {"type": "object", "properties": {"data": {"type": "string"}}}
    
    try:
        result = await extractor.extract(text, schema)
        
        if result.is_success():
            print("✅ 提取成功")
            print(f"数据: {result.extracted_data}")
            
        elif result.status.value == "partial":
            print("⚠️ 部分成功")
            print(f"已提取数据: {result.extracted_data}")
            print("错误信息:")
            for error in result.errors:
                print(f"  - {error.error_type}: {error.error_message}")
                
        else:
            print("❌ 提取失败")
            print("错误信息:")
            for error in result.errors:
                print(f"  - {error.error_type}: {error.error_message}")
                if error.error_details:
                    print(f"    详情: {error.error_details}")
        
        # 输出元数据
        print(f"\n元数据:")
        print(f"  处理时间: {result.processing_time:.3f}秒")
        for key, value in result.metadata.items():
            print(f"  {key}: {value}")
            
    except Exception as e:
        print(f"程序异常: {str(e)}")
```

## 不同LLM提供商配置

### OpenAI

```python
config = ExtractionConfig(
    llm_base_url="https://api.openai.com/v1",
    llm_api_key="sk-...",
    llm_model="gpt-4"
)
```

### Azure OpenAI

```python
config = ExtractionConfig(
    llm_base_url="https://your-resource.openai.azure.com/",
    llm_api_key="your-azure-key",
    llm_model="gpt-4",
    extra_headers={"api-version": "2023-12-01-preview"}
)
```

### 其他兼容服务

```python
# Anthropic Claude (通过代理)
config = ExtractionConfig(
    llm_base_url="https://api.anthropic.com/v1",
    llm_api_key="your-anthropic-key",
    llm_model="claude-3-sonnet"
)

# 本地部署的模型
config = ExtractionConfig(
    llm_base_url="http://localhost:8000/v1",
    llm_api_key="local-key",
    llm_model="local-model"
)
```

## 批量处理示例

```python
async def batch_processing():
    config = ExtractionConfig(llm_api_key="your-api-key")
    extractor = DocumentExtractor(config)
    
    # 批量文档
    documents = [
        {"id": "doc1", "content": "文档1内容..."},
        {"id": "doc2", "content": "文档2内容..."},
        {"id": "doc3", "content": "文档3内容..."}
    ]
    
    schema = {
        "type": "object",
        "properties": {
            "summary": {"type": "string", "description": "文档摘要"}
        }
    }
    
    results = []
    
    for doc in documents:
        print(f"处理文档: {doc['id']}")
        
        result = await extractor.extract(
            text_content=doc["content"],
            schema=schema,
            document_id=doc["id"]
        )
        
        results.append({
            "document_id": doc["id"],
            "success": result.is_success(),
            "data": result.extracted_data,
            "errors": [e.error_message for e in result.errors]
        })
    
    # 统计结果
    success_count = sum(1 for r in results if r["success"])
    print(f"\n批量处理完成: {success_count}/{len(documents)} 成功")
    
    return results
```
